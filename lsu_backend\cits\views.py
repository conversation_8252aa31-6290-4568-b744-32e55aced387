from rest_framework.response import Response
from rest_framework.views import APIView
from .forms import LSUPartnerGM<PERSON><PERSON>Form, LSUDownloadFileForm, LSUVolumeFileForm, LSUOtherSocialMediaForm, CampusDirectoryForm
from .models import LSUPartnerGMAIL, LSUDownloadFile, LSUVolumeFile, LSUOtherSocialMedia, CampusDirectory
from .serializers import LSUPartnerGMAILSerializer, LSUDownloadFileSerializer, LSUVolumeFileSerializer, LSUOtherSocialMediaSerializer, CampusDirectorySerializer

class ListLSUPartnerGMAILView(APIView):
  def get(self, request, format=None):
    obj = LSUPartnerGMAIL.objects.all()
    serializer = LSUPartnerGMAILSerializer(obj, many=True)

    return Response(serializer.data)

class CreateLSUPartnerGMAILView(APIView):
  def post(self, request):
    form = LSUPartnerGMAILForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = LSUPartnerGMAIL.objects.get(pk=pk)
    form = LSUPartnerGMAILForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = LSUPartnerGMAIL.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class LSUPartnerGMAILDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = LSUPartnerGMAIL.objects.get(pk=pk)
    serializer = LSUPartnerGMAILSerializer(obj)

    return Response(serializer.data)

class ListLSUDownloadFileView(APIView):
  def get(self, request, format=None):
    obj = LSUDownloadFile.objects.all()
    serializer = LSUDownloadFileSerializer(obj, many=True)

    return Response(serializer.data)

class CreateLSUDownloadFileView(APIView):
  def post(self, request):
    form = LSUDownloadFileForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = LSUDownloadFile.objects.get(pk=pk)
    form = LSUDownloadFileForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = LSUDownloadFile.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class LSUDownloadFileDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = LSUDownloadFile.objects.get(pk=pk)
    serializer = LSUDownloadFileSerializer(obj)

    return Response(serializer.data)

class ListLSUVolumeFileView(APIView):
  def get(self, request, format=None):
    obj = LSUVolumeFile.objects.all()
    serializer = LSUVolumeFileSerializer(obj, many=True)

    return Response(serializer.data)

class CreateLSUVolumeFileView(APIView):
  def post(self, request):
    form = LSUVolumeFileForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = LSUVolumeFile.objects.get(pk=pk)
    form = LSUVolumeFileForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = LSUVolumeFile.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class LSUVolumeFileDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = LSUVolumeFile.objects.get(pk=pk)
    serializer = LSUVolumeFileSerializer(obj)

    return Response(serializer.data)

class ListLSUsmlFileView(APIView):
  def get(self, request, format=None):
    obj = LSUOtherSocialMedia.objects.all()
    serializer = LSUOtherSocialMediaSerializer(obj, many=True)

    return Response(serializer.data)

class CreateLSUsmlFileView(APIView):
  def post(self, request):
    form = LSUOtherSocialMediaForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = LSUOtherSocialMedia.objects.get(pk=pk)
    form = LSUOtherSocialMediaForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = LSUOtherSocialMedia.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class LSUsmlFileDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = LSUOtherSocialMedia.objects.get(pk=pk)
    serializer = LSUOtherSocialMediaSerializer(obj)

    return Response(serializer.data)
  
class ListDirectoryView(APIView):
  def get(self, request, format=None):
    obj = CampusDirectory.objects.all()
    serializer = CampusDirectorySerializer(obj, many=True)

    return Response(serializer.data)

class CreateDirectoryView(APIView):
  def post(self, request):
    form = CampusDirectoryForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = CampusDirectory.objects.get(pk=pk)
    form = CampusDirectoryForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = LSUOtherSocialMedia.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class DirectoryDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = CampusDirectory.objects.get(pk=pk)
    serializer = CampusDirectorySerializer(obj)

    return Response(serializer.data)