# Generated by Django 5.0.2 on 2025-04-25 10:17

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CampusPassEntryFormVersionThreeModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incharge_firstname', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('incharge_middlename', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('incharge_lastname', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('incharge_contact_email', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('incharge_contact_number', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('type_of_access', django_jsonform.models.fields.JSONField(null=True)),
                ('schedule', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('approved_activities_link', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('approved_gso_docs_link', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('attendees', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('name_list', django_jsonform.models.fields.JSONField(null=True)),
                ('approval_status', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('remarks', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('tracking_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('purpose', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('checkin', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='CampusPassGmailNotificationPending',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incharge_firstname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('incharge_contact_email', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('schedule', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('approval_status', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('remarks', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('tracking_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('purpose', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
