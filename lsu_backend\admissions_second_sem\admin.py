from django.contrib import admin
from .models import EnrolleeDataFirstSemModel, EnrolleeDataFirstSemNewStudentModel, EnrolleeDataFirstSemTransfereeStudentModel, EnrolleeDataFirstSemSecondDegreeHolderStudentModel, StudentProfileModel, EvaluationFormDataModel, ReceiptDataModel, AdmissionFormCompleteEmailNotificationDataModel, AdmissionsDoneEmailNotificationDataModel, AdvisingDoneEmailNotificationDataModel, AccountingDoneEmailNotificationDataModel, EvaluationDoneEmailNotificationDataModel, ValidationEmailNotificationDataModel, NewTrackingIDEmailNotificationDataModel

# class StudentProfileModelAdmin(admin.ModelAdmin):
#     list_display = ['id','primary_info','father_contact_info','father_employment_info','father_personal_info','household_capacity_and_access_to_distance_learning','how_you_learn_about_lsu','legal_guardian_contact_info','legal_guardian_employment_info',
#     'legal_guardian_personal_info', 'mother_contact_info', 'mother_employment_info', 'mother_personal_info', 'reasons_for_choosing_lsu', 'student_choice', 'student_contact_info', 'student_education_information_number', 'student_educational_info',  'student_emergency_contact_information', 'student_tribal_or_indigenous_community', 'created_at']

# class EnrolleeDataFirstSemModelAdmin(admin.ModelAdmin):
    # list_per_page = EnrolleeDataFirstSemModel.objects.all().count()
    # list_max_show_all = list_per_page
    # list_display = ['tracking_id','student_lsu_id_number','lastname','firstname','college', 'program', 'contact_personal_email_address', 'contact_lsu_email_address']

# class StudentProfileModelAdmin(admin.ModelAdmin):
    # list_per_page = StudentProfileModel.objects.all().count()
    # list_max_show_all = list_per_page
    # list_display = ['id','primary_info', 'created_at']

# class EnrolleeDataFirstSemNewStudentModelAdmin(admin.ModelAdmin):
    # list_per_page = EnrolleeDataFirstSemNewStudentModel.objects.all().count()
    # list_max_show_all = list_per_page
    # list_display = ['tracking_id','student_lsu_id_number','lastname','firstname','college', 'program', 'contact_personal_email_address', 'contact_lsu_email_address']

# class EnrolleeDataFirstSemTransfereeStudentModelAdmin(admin.ModelAdmin):
    # list_per_page = EnrolleeDataFirstSemTransfereeStudentModel.objects.all().count()
    # list_max_show_all = list_per_page
    # list_display = ['tracking_id','student_lsu_id_number','lastname','firstname','college', 'program', 'contact_personal_email_address', 'contact_lsu_email_address']

# class EnrolleeDataFirstSemSecondDegreeHolderStudentModelAdmin(admin.ModelAdmin):
    # list_per_page = EnrolleeDataFirstSemSecondDegreeHolderStudentModel.objects.all().count()
    # list_max_show_all = list_per_page
    # list_display = ['tracking_id','student_lsu_id_number','lastname','firstname','college', 'program', 'contact_personal_email_address', 'contact_lsu_email_address']

# admin.site.register(EnrolleeDataFirstSemModel, EnrolleeDataFirstSemModelAdmin)
# admin.site.register(EnrolleeDataFirstSemNewStudentModel, EnrolleeDataFirstSemNewStudentModelAdmin)
# admin.site.register(EnrolleeDataFirstSemTransfereeStudentModel, EnrolleeDataFirstSemTransfereeStudentModelAdmin)
# admin.site.register(EnrolleeDataFirstSemSecondDegreeHolderStudentModel, EnrolleeDataFirstSemSecondDegreeHolderStudentModelAdmin)
# admin.site.register(StudentProfileModel, StudentProfileModelAdmin)

class StudentProfileModelAdmin(admin.ModelAdmin):
    list_display = ['id','primary_info_lastname', 'primary_info_firstname', 'primary_info_college', 'created_at']

admin.site.register(EnrolleeDataFirstSemModel)
admin.site.register(EnrolleeDataFirstSemNewStudentModel)
admin.site.register(EnrolleeDataFirstSemTransfereeStudentModel)
admin.site.register(EnrolleeDataFirstSemSecondDegreeHolderStudentModel)
admin.site.register(StudentProfileModel, StudentProfileModelAdmin)
admin.site.register(EvaluationFormDataModel)
admin.site.register(ReceiptDataModel)
admin.site.register(AdmissionFormCompleteEmailNotificationDataModel)
admin.site.register(AdmissionsDoneEmailNotificationDataModel)
admin.site.register(AdvisingDoneEmailNotificationDataModel)
admin.site.register(AccountingDoneEmailNotificationDataModel)
admin.site.register(EvaluationDoneEmailNotificationDataModel)
admin.site.register(ValidationEmailNotificationDataModel)
admin.site.register(NewTrackingIDEmailNotificationDataModel)