from django.urls import path
from . import views

urlpatterns = [
     path('submit-status-to-gmail/<int:pk>/', views.SubmitStatusToGmail.as_view()),
     path('submit-appointment-to-gmail/', views.SubmitAppointmentToGmail.as_view()),
     path('booking/list/', views.ListBookingLibraryView.as_view()),
     path('booking/create/', views.CreateBookingLibraryView.as_view()),
     path('booking/<int:pk>/', views.BookingLibraryDetailView.as_view()),
     path('booking/<int:pk>/edit/', views.CreateBookingLibraryView.as_view()),
     path('booking/<int:pk>/delete/', views.CreateBookingLibraryView.as_view()),
     path('schedule/booking/list/', views.ListLibraryBookingSchedulesView.as_view()),
     path('schedule/booking/create/', views.CreateLibraryBookingScheduleView.as_view()),
     path('schedule/booking/<int:pk>/', views.LibraryBookingSchedulesDetailView.as_view()),
     path('schedule/booking/<int:pk>/edit/', views.CreateLibraryBookingScheduleView.as_view()),
     path('schedule/booking/<int:pk>/delete/', views.CreateLibraryBookingScheduleView.as_view()), 
     path('reports/book/list/', views.ListLRCShelflistReportsView.as_view()),
     path('reports/book/<int:pk>/', views.CRUDShelflistReportDetailView.as_view()),
     path('reports/book/create/', views.CRUDShelflistReportView.as_view()),
     path('reports/book/edit/<int:pk>/', views.CRUDShelflistReportView.as_view()),
     path('reports/book/delete/<int:pk>/', views.CRUDShelflistReportView.as_view()),  
]

