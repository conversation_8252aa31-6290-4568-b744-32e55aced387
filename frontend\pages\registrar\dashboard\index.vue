<script setup>
import { onMounted, ref, onBeforeUnmount } from "vue";
import { useUserStore } from "@/stores/user";
const router = useRouter();
const userStore = useUserStore();
import _ from "lodash";
import moment from "moment";

const listItems = ref([]);
let tableDisplay = ref(true);
let toggleSideBarMenu = ref(false);
let toggleConfirmDelete = ref(false);

const endpoint = ref(userStore.mainDevServer);
let selectedID = ref(null);
const statusFilter = ref("all");

const showImageModal = ref(false);
const currentModalImage = ref("");

// Add these refs for the personal info modal
const showPersonalInfoModal = ref(false);
const currentPersonalInfo = ref(null);

const openPersonalInfoModal = (item) => {
  currentPersonalInfo.value = item;
  showPersonalInfoModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closePersonalInfoModal = () => {
  showPersonalInfoModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const exportCSV = () => {
  if (!listItems.value.length) return;

  const fields = [
    "firstname",
    "middlename",
    "lastname",
    "birthdate",
    "mother_maiden_name",
    "contact_number",
    "email",
    "alumni",
    "college",
    "course",
    "year_graduated_last_attended",
    "type_document_requests",
    "details",
    "tracking_id",
    "logs",
  ];

  const cleanValue = (val, field) => {
    if (Array.isArray(val)) {
      return val
        .map((v) => {
          if (typeof v === "object") return Object.values(v).join(" ");
          return v;
        })
        .join(field === "type_document_requests" ? "\n" : "; ");
    }

    if (typeof val === "object" && val !== null) {
      return Object.values(val).join(" ");
    }

    return String(val ?? "")
      .replace(/[\{\}\\"]/g, "")
      .replace(/\s+/g, " ")
      .trim();
  };

  const csvRows = [
    fields.join(","), // Header row
    ...listItems.value.map((item) =>
      fields
        .map((field) => {
          const value = cleanValue(item[field], field);
          return `"${value}"`; // Keep quotes for Excel to recognize newlines
        })
        .join(",")
    ),
  ];

  const csvContent = new Blob([csvRows.join("\n")], {
    type: "text/csv;charset=utf-8;",
  });
  const url = URL.createObjectURL(csvContent);
  const link = document.createElement("a");
  link.href = url;
  link.download = "registrar_list.csv";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const openImageModal = (imageUrl) => {
  currentModalImage.value = imageUrl;
  showImageModal.value = true;
  isModalOpen.value = true;
  // Prevent scrolling on the body when modal is open
  document.body.style.overflow = "hidden";
};

const closeImageModal = () => {
  showImageModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const showLogsModal = ref(false);
const showPaymentModal = ref(false);
const currentItem = ref(null);
const newLogRemarks = ref("");
const defaultStatus = ref("");

const openLogsModal = (item) => {
  currentItem.value = item;
  showLogsModal.value = true;
  isModalOpen.value = true;
  newLogRemarks.value = "";
  document.body.style.overflow = "hidden";

  currentItemInfo.value.fullname = `${item.firstname} ${item.middlename} ${item.lastname}`;
  currentItemInfo.value.email = item.email;
  currentItemInfo.value.course = item.course;

  const types = item.type_document_requests || [];
  const details = item.details || [];

  currentItemInfo.value.detail_fees = types.map((docName, index) => ({
    fee_name: docName,
    amount: details[index]?.amount || "", // fallback to empty string if no amount
  }));

  console.log("Mapped Detail Fees:", currentItemInfo.value.detail_fees);
};

const closeLogsModal = () => {
  showLogsModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const openPaymentModal = (item) => {
  currentItem.value = item;
  showPaymentModal.value = true;
  isModalOpen.value = true;
  document.body.style.overflow = "hidden";

  currentItemInfo.value.fullname = `${item.firstname} ${item.middlename} ${item.lastname}`;
  currentItemInfo.value.email = item.email;
  currentItemInfo.value.course = item.course;

  const types = item.type_document_requests || [];
  const details = item.details || [];

  currentItemInfo.value.detail_fees = types.map((docName, index) => ({
    fee_name: docName,
    amount: details[index]?.amount || "", // fallback to empty string if no amount
  }));

  console.log("Mapped Detail Fees:", currentItemInfo.value.detail_fees);
};

const closePaymentModal = () => {
  showPaymentModal.value = false;
  isModalOpen.value = checkIfModalOpen();
  // Restore scrolling
  document.body.style.overflow = "auto";
};

const showEmailConfirmation = ref(false);
const isEmailSending = ref(false);
const pendingLogRemarks = ref("");

const addNewLog = async () => {
  if (!newLogRemarks.value.trim() || !currentItem.value) return;

  try {
    // Store the remarks for potential email
    pendingLogRemarks.value = newLogRemarks.value.trim();

    // Create the new log entry
    const newLog = {
      timestamp: moment().format("MMMM DD, YYYY h:mm:ss A"),
      status_remarks: pendingLogRemarks.value,
    };

    // Create a copy of the current logs array or initialize a new one
    const updatedLogs = currentItem.value.logs
      ? [...currentItem.value.logs]
      : [];

    // Add the new log to the beginning of the array
    updatedLogs.unshift(newLog);

    // Set the ID for the API call
    selectedID.value = currentItem.value.id;

    // Create a complete copy of the current item to send to the server
    const updatedItem = { ...currentItem.value, logs: updatedLogs };

    // Update the item with the new logs using the existing endpoint
    await $fetch(
      endpoint.value + "/api/registrar/" + selectedID.value + "/edit/",
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: updatedItem,
      }
    );

    // Update the local data
    currentItem.value.logs = updatedLogs;

    // Clear the input field
    newLogRemarks.value = "";

    // Refresh the list data in the background without closing the modal
    await fetchListItemsQuietly();

    // Show email confirmation dialog
    showEmailConfirmation.value = true;
  } catch (error) {
    console.error("Error adding log:", error);
    // Optional: Show error message
    // toast.error("Failed to add log");
  }
};

const cancelEmailConfirmation = () => {
  showEmailConfirmation.value = false;
  pendingLogRemarks.value = "";
};

const confirmAndSendEmail = async () => {
  if (!currentItem.value) return;

  isEmailSending.value = true;

  try {
    // Prepare the data for the email API
    const emailData = {
      id: currentItem.value.id, // Make sure we're sending the ID
      latest_status: pendingLogRemarks.value,
    };

    console.log("Sending email data:", emailData); // Debug log

    // Send the email notification
    await $fetch(endpoint.value + "/api/registrar/confirmation/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: emailData,
    });

    // Show success message or notification
    console.log("Email sent successfully");
    // toast.success("Status update email sent successfully");

    // Close the confirmation dialog
    showEmailConfirmation.value = false;
    pendingLogRemarks.value = "";
  } catch (error) {
    console.error("Error sending confirmation email:", error);
    // Show error message
    // toast.error("Failed to send email notification");
  } finally {
    isEmailSending.value = false;
  }
};

const selectedCollege = ref("");
const colleges = ref([
  {
    value: "Arts and Sciences, Engineering, Architecture, Computer Studies",
    label: "Arts and Sciences, Engineering, Architecture, Computer Studies",
  },
  {
    value: "Business-Related Courses, BSTM",
    label: "Business-Related Courses, BSTM",
  },
  {
    value: "Nursing, and Graduate Studies",
    label: "Nursing, and Graduate Studies",
  },
  { value: "Education Courses, BSHM", label: "Education Courses, BSHM" },
  { value: "Criminology", label: "Criminology" },
]);

const sortColumn = ref(null);
const sortDirection = ref("asc");
const collegeFilterList = ref(false);

onMounted(async () => {
  const email = userStore.user.email;

  const emailToCollegeMap = {
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>": "Business-Related Courses, BSTM",
    "<EMAIL>": "Nursing, and Graduate Studies",
    "<EMAIL>": "Education Courses, BSHM",
    "<EMAIL>": "Criminology",
  };

  const fullAccessEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];

  const authorizedEmails = [
    ...fullAccessEmails,
    ...Object.keys(emailToCollegeMap),
  ];

  window.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && showImageModal.value) {
      closeImageModal();
    }
  });

  if (userStore.user.isAuthenticated && authorizedEmails.includes(email)) {
    // Set selected college for non-full-access users
    if (!fullAccessEmails.includes(email)) {
      selectedCollege.value = emailToCollegeMap[email] || "";
    } else {
      // Enable college filter dropdown for full access users
      collegeFilterList.value = true;
    }

    await fetchListItems();
    router.push("/registrar/dashboard");
    startAutoRefresh();
  } else {
    router.push("/unauthorized");
  }
});

const refreshInterval = 1000;
let refreshTimer = null;
let isModalOpen = ref(false);
let isSelectingAll = ref(false);

const checkIfModalOpen = () => {
  return (
    showPaymentModal.value ||
    showLogsModal.value ||
    showImageModal.value ||
    toggleConfirmDelete.value ||
    showPersonalInfoModal.value
  );
};

const startAutoRefresh = () => {
  // Clear any existing timer
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }

  // Set up new interval timer
  refreshTimer = setInterval(async () => {
    // Skip refresh if a modal is open or if selectAllItems is running
    if (!checkIfModalOpen() && !isSelectingAll.value) {
      await fetchListItemsQuietly();
      await checkAndRemoveDuplicates();
    }
  }, refreshInterval);
};

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

const checkAndRemoveDuplicates = async () => {
  stopAutoRefresh();
  try {
    // Get all items
    const items = listItems.value;
    if (!items || !Array.isArray(items) || items.length === 0) return;

    // Find duplicate tracking_ids
    const trackingIds = {};
    const duplicates = [];

    items.forEach((item) => {
      if (!item.tracking_id) return;

      if (trackingIds[item.tracking_id]) {
        // This is a duplicate, keep the one with the earlier created_at
        const existingItem = trackingIds[item.tracking_id];
        const duplicateToRemove =
          new Date(existingItem.created_at) > new Date(item.created_at)
            ? existingItem.id
            : item.id;

        duplicates.push(duplicateToRemove);
      } else {
        trackingIds[item.tracking_id] = item;
      }
    });

    // Delete duplicates
    for (const id of duplicates) {
      console.log(`Removing duplicate item with ID: ${id}`);
      await $fetch(endpoint.value + "/api/registrar/" + id + "/delete/", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // If any duplicates were removed, refresh the list
    if (duplicates.length > 0) {
      console.log(`Removed ${duplicates.length} duplicate entries`);
      await fetchListItems();
    }
  } catch (error) {
    console.error("Error checking for duplicates:", error);
  } finally {
    startAutoRefresh();
  }
};

const fetchListItemsQuietly = async () => {
  try {
    const updatedItems =
      (await $fetch(endpoint.value + "/api/registrar/list").catch(
        (error) => error.data
      )) || [];

    // Update the list items without affecting search or other UI state
    if (updatedItems.length > 0) {
      // If we have a search active, we need to preserve it
      if (originalListItems.value.length > 0) {
        // Update the original list
        originalListItems.value = updatedItems;

        // Re-apply the current search filter
        performSearch();
      } else {
        // No search active, just update the list
        listItems.value = updatedItems;
      }
    }

    // Update the current item with fresh data
    if (currentItem.value) {
      const updatedCurrentItem = updatedItems.find(
        (item) => item.id === currentItem.value.id
      );
      if (updatedCurrentItem) {
        currentItem.value = updatedCurrentItem;
      }
    }
  } catch (error) {
    console.error("Error fetching list items quietly:", error);
  }
};

const fetchListItems = async () => {
  isLoading.value = true;
  try {
    const response = await $fetch(endpoint.value + "/api/registrar/list").catch(
      (error) => {
        console.error("Fetch error:", error);
        return [];
      }
    );

    // Ensure listItems is always an array
    listItems.value = Array.isArray(response) ? response : [];

    // Reset search state
    originalListItems.value = [];
    searchQuery.value = "";
  } catch (error) {
    console.error("Error fetching list items:", error);
    listItems.value = []; // fallback
  } finally {
    isLoading.value = false;
  }
};

onBeforeUnmount(() => {
  stopAutoRefresh();
});

const logOut = () => {
  router.push("/registrar/login");
  userStore.removeToken();
};

const selectedItems = ref([]);
const isDeleting = ref(false);

const allSelected = computed(() => {
  return (
    selectedItems.value.length === filteredByCollege.value.length &&
    filteredByCollege.value.length > 0
  );
});

const selectAllItems = () => {
  console.log("selectAllItems called");
  console.log("allSelected.value:", allSelected.value);
  stopAutoRefresh();
  isSelectingAll.value = true;
  if (allSelected.value) {
    // Deselect all
    selectedItems.value = [];
  } else {
    // Select all
    console.log("filteredByCollege.value:", filteredByCollege.value);
    selectedItems.value = filteredByCollege.value.map((item) => item.id);
  }
  console.log("selectedItems.value:", selectedItems.value);
  startAutoRefresh();
  isSelectingAll.value = false;
};

const toggleDeleteMultiple = () => {
  if (selectedItems.value.length === 0) return;
  toggleConfirmDelete.value = true;
  isModalOpen.value = true;
};

const cancelDelete = () => {
  toggleConfirmDelete.value = false;
  isModalOpen.value = checkIfModalOpen();
};

const deleteItems = async () => {
  if (selectedItems.value.length === 0) return;

  try {
    // Show loading state
    isDeleting.value = true;

    // Delete each selected item
    for (const id of selectedItems.value) {
      await $fetch(endpoint.value + "/api/registrar/" + id + "/delete/", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });
    }

    // Success handling
    console.log(`${selectedItems.value.length} items deleted successfully`);

    // Clear selection
    selectedItems.value = [];

    // Refresh the list
    await fetchListItems();

    // Close the modal
    toggleConfirmDelete.value = false;
    isModalOpen.value = checkIfModalOpen();
  } catch (error) {
    console.error("Error deleting items:", error);
  } finally {
    // Reset loading state
    isDeleting.value = false;
  }
};

let filteredItems;
const filteredListItems = computed(() => {
  let items = [...listItems.value];

  // Filter by status in logs
  if (statusFilter.value === "done") {
    items = items.filter((item) =>
      item.logs?.some((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return statusRemarks.includes("transaction closed");
      })
    );
  } else if (statusFilter.value === "pending") {
    items = items.filter((item) =>
      item.logs?.every((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return !statusRemarks.includes("transaction closed");
      })
    );
  }

  return _.orderBy(items, "created_at", "asc");
});

const doneCount = (college) => {
  return filteredListItems.value.filter(
    (item) =>
      item.college === college &&
      item.logs?.some((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return statusRemarks.includes("transaction closed");
      })
  ).length;
};

const pendingCount = (college) => {
  return filteredListItems.value.filter(
    (item) =>
      item.college === college &&
      item.logs?.every((log) => {
        const statusRemarks = log?.status_remarks?.toLowerCase() || "";
        return !statusRemarks.includes("transaction closed");
      })
  ).length;
};

const totalCount = (college) => {
  return filteredListItems.value.filter((item) => item.college === college)
    .length;
};

const sortColleges = (column) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
  } else {
    sortColumn.value = column;
    sortDirection.value = "asc";
  }

  colleges.value.sort((a, b) => {
    let valueA, valueB;

    if (column === "Pending") {
      valueA = pendingCount(a.value);
      valueB = pendingCount(b.value);
    } else if (column === "Done") {
      valueA = doneCount(a.value);
      valueB = doneCount(b.value);
    } else if (column === "Total") {
      valueA = totalCount(a.value);
      valueB = totalCount(b.value);
    } else {
      // Default to sorting by department label
      valueA = a.label;
      valueB = b.label;
    }

    if (valueA < valueB) {
      return sortDirection.value === "asc" ? -1 : 1;
    }
    if (valueA > valueB) {
      return sortDirection.value === "asc" ? 1 : -1;
    }
    return 0;
  });
};

const getEmailsForCollege = (college) => {
  const emailToCollegeMap = {
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>":
      "Arts and Sciences, Engineering, Architecture, Computer Studies",
    "<EMAIL>": "Business-Related Courses, BSTM",
    "<EMAIL>": "Nursing, and Graduate Studies",
    "<EMAIL>": "Education Courses, BSHM",
    "<EMAIL>": "Criminology",
  };

  return Object.keys(emailToCollegeMap).filter(
    (email) => emailToCollegeMap[email] === college
  );
};

const isLoading = ref(true);
const currentPage = ref(1);
const itemsPerPage = 500;

const maxVisiblePages = 4;

const totalPages = computed(() => {
  return Math.ceil(filteredListItems.value.length / itemsPerPage);
});

const paginatedListItems = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredListItems.value.slice(startIndex, endIndex);
});

const filteredByCollege = computed(() => {
  console.log("filteredByCollege computed");
  console.log("selectedCollege.value:", selectedCollege.value);
  console.log("paginatedListItems.value:", paginatedListItems.value);
  if (!selectedCollege.value) {
    return paginatedListItems.value;
  }
  if (!selectedCollege.value) {
    return paginatedListItems.value;
  }
  const filtered = paginatedListItems.value.filter(
    (item) => item.college === selectedCollege.value
  );
  console.log("filtered:", filtered);
  return filtered;
});

const visiblePages = computed(() => {
  const pages = [];
  let startPage = Math.max(
    1,
    currentPage.value - Math.floor(maxVisiblePages / 2)
  );
  let endPage = Math.min(totalPages.value, startPage + maxVisiblePages - 1);

  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  return pages;
});

// const showPersonalInfoModal = ref(false);

onBeforeUnmount(() => {
  window.removeEventListener("keydown", (e) => {
    if (e.key === "Escape" && showImageModal.value) {
      closeImageModal();
    }
  });
  // Also make sure to clean up the auto-refresh
  stopAutoRefresh();
});

const searchQuery = ref("");
const originalListItems = ref([]);

const performSearch = () => {
  // Reset to page 1 when searching
  currentPage.value = 1;

  // Store original list if this is the first search
  if (originalListItems.value.length === 0 && listItems.value.length > 0) {
    originalListItems.value = [...listItems.value];
  }

  // Perform the search
  const query = searchQuery.value.toLowerCase().trim();

  // Filter the items based on search query
  const filteredItems =
    originalListItems.value.length > 0
      ? originalListItems.value
      : listItems.value;

  listItems.value = filteredItems.filter((item) => {
    // Search in all string properties of the item
    if (!item) return false;

    const query = searchQuery.value.toLowerCase().trim();

    // Check all properties of the item
    return Object.keys(item).some((key) => {
      // Only search in string properties
      if (typeof item[key] === "string") {
        return item[key].toLowerCase().includes(query);
      }
      return false;
    });
  });

  // Clear selection when search results change
  selectedItems.value = [];
};

// Add a computed property to sort logs by timestamp (oldest first)
const sortedLogs = computed(() => {
  if (!currentItem.value?.logs || !Array.isArray(currentItem.value.logs)) {
    return [];
  }

  // Create a copy of the logs array to avoid modifying the original
  return [...currentItem.value.logs].sort((a, b) => {
    // Parse timestamps using moment for accurate comparison
    const dateA = moment(a.timestamp, "MMMM DD, YYYY h:mm:ss A");
    const dateB = moment(b.timestamp, "MMMM DD, YYYY h:mm:ss A");

    // Sort in ascending order (oldest first)
    return dateA.valueOf() - dateB.valueOf();
  });
});

watch(listItems, (val) => {
  console.log("listItems updated:", val);
});

watch(selectedCollege, () => {
  selectedItems.value = [];
});

watch(statusFilter, () => {
  selectedItems.value = [];
});

const isFullAccess = computed(() => {
  return (
    userStore.user.email === "<EMAIL>" ||
    userStore.user.email === "<EMAIL>"
  );
});

// Dummy data structure
const currentItemInfo = ref({
  payment_id: "PID" + moment().valueOf(),
  fullname: "",
  email: "",
  course: "",
  date_graduated_last_attended: "",
  total: "0",
  detail_fees: [],
});

const addDetailFee = () => {
  currentItemInfo.value.detail_fees.push({ fee_name: "", amount: "" });
  updateTotal();
};

// Remove fee row
const removeDetailFee = (index) => {
  currentItemInfo.value.detail_fees.splice(index, 1);
  updateTotal();
};

// Recalculate total
const updateTotal = () => {
  let total = 0;
  currentItemInfo.value.detail_fees.forEach((fee) => {
    total += Number((fee.amount || 0).toString());
  });
  const formatted = total.toFixed(2);
  currentItemInfo.value.total = formatted;
  // requestPaymentFee.value.total = formatted;
};

// Reactively update total when fee amounts change
watch(
  () => currentItemInfo.value.detail_fees,
  () => updateTotal(),
  { deep: true }
);

// Send email
const sendEmailDetailFee = async () => {
  // console.log(currentItemInfo.value);
  try {
    await $fetch(endpoint.value + "/api/registrar/payment/fees/", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: currentItemInfo.value,
    });
    // alert("Email sent successfully");
  } catch (error) {
    console.error("Error sending email:", error);
  }
};
</script>
<template>
  <div>
    <div class="h-screen flex">
      <div
        class="pb-3 lg:w-3/12 bg-gray-100 w-full flex overflow-hidden"
        v-show="toggleSideBarMenu"
      >
        <div class="w-full">
          <div
            class="flex items-center justify-center text-white bg-green-900 lg:py-[16px] py-[8px] sta"
          >
            <div class="flex items-center w-full px-2">
              <i class="fa fa-user mx-2" aria-hidden="true"></i>
              <h1 class="text-sm">
                {{ userStore.user.email }}
              </h1>
            </div>

            <div
              @click="toggleSideBarMenu = !toggleSideBarMenu"
              class="w-10 px-1.5 lg:hidden flex"
            >
              <i
                class="fa text-3xl text-white"
                :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'"
                aria-hidden="true"
              ></i>
            </div>
          </div>

          <div class="">
            <div class="w-fit mx-auto mt-5 mb-3">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/logos/LSU_Seal.PNG"
                class="lg:w-24 w-20 mx-auto"
              />
            </div>

            <div class="text-center">
              <h1 class="font-bold text-green-800 text-2xl">Dashboard</h1>
            </div>

            <div class="mx-auto mt-10 mb-5 grid grid-cols-1">
              <a
                href="/registrar/dashboard"
                class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 font-bold text-left text-black hover:bg-black hover:text-white"
              >
                <i class="fa fa-list mr-3" aria-hidden="true"></i>
                All Request Lists
              </a>

              <a
                href="/"
                class="text-xs mx-auto mb-2 w-full uppercase whitespace-nowrap px-5 py-1 font-bold text-left text-green-900 hover:bg-green-900 hover:text-white"
              >
                <i class="fa fa-globe mr-3" aria-hidden="true"></i>
                LSU HOME PAGE
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full overflow-y-scroll">
        <div class="bg-green-800 fixed w-full z-10">
          <div class="flex mx-auto justify-between py-2 px-3.5">
            <div
              @click="toggleSideBarMenu = !toggleSideBarMenu"
              class="w-auto flex items-center lg:px-1.5"
            >
              <i
                class="fa text-3xl text-white"
                :class="toggleSideBarMenu ? 'fa-caret-left' : 'fa-bars'"
                aria-hidden="true"
              ></i>
              <p
                class="text-white whitespace-nowrap lg:ml-5 ml-3 font-bold uppercase lg:text-sm text-xs"
              >
                Registrar
              </p>
            </div>

            <button @click="logOut" class="flex hover:font-bold pt-1">
              <i class="fa fa-sign-out text-white text-xl"></i>
              <h1 class="text-xs text-white p-1.5 lg:flex hidden">Log Out</h1>
            </button>
          </div>
        </div>

        <div
          v-if="isFullAccess"
          class="w-11/12 mx-auto shadow-lg border-2 border-green-600 rounded-lg mt-20"
        >
          <div>
            <ul class="flex border-b font-bold border-green-800 uppercase">
              <li class="w-7/12 px-36 text-left">Department</li>
              <li
                @click="sortColleges('Pending')"
                class="w-2/12 text-center cursor-pointer"
              >
                Pending
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Pending' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Pending' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Pending',
                  }"
                ></i>
              </li>
              <li
                @click="sortColleges('Done')"
                class="w-2/12 text-center cursor-pointer"
              >
                Done
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Done' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Done' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Done',
                  }"
                ></i>
              </li>
              <li
                @click="sortColleges('Total')"
                class="w-2/12 text-center cursor-pointer"
              >
                Total
                <i
                  :class="{
                    'fa fa-sort-up':
                      sortColumn === 'Total' && sortDirection === 'asc',
                    'fa fa-sort-down':
                      sortColumn === 'Total' && sortDirection === 'desc',
                    'fa fa-sort': sortColumn !== 'Total',
                  }"
                ></i>
              </li>
            </ul>
          </div>
          <div>
            <ul>
              <li
                class="border-b"
                v-for="college in colleges"
                :key="college.value"
              >
                <ul class="flex">
                  <li class="w-7/12 text-left px-36">
                    {{ college.label }}
                    <div class="text-xs text-gray-500">
                      {{ getEmailsForCollege(college.value).join(", ") }}
                    </div>
                  </li>

                  <li class="w-2/12 text-center">
                    {{ pendingCount(college.value) }}
                  </li>
                  <li class="w-2/12 text-center">
                    {{ doneCount(college.value) }}
                  </li>
                  <li class="w-2/12 text-center">
                    {{ totalCount(college.value) }}
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>

        <div class="">
          <div class="w-full lg:p-5 px-2 py-2">
            <div v-show="tableDisplay">
              <div
                class="w-full shadow bg-gray-100 text-green-900 font-bold px-2 text-center mb-3 py-2 text-xs uppercase"
              >
                All Request Lists
              </div>

              <div
                class="w-full flex justify-between items-center mb-4 px-2 py-2 bg-white shadow rounded-md"
              >
                <div class="lg:flex justify-between w-full gap-x-3">
                  <div class="flex justify-between gap-x-2">
                    <div class="">
                      <button
                        @click="selectAllItems"
                        class="whitespace-nowrap flex items-center px-2 bg-gray-200 hover:bg-gray-300 text-gray-700 lg:px-3 pr-2 py-1.5 rounded-md transition-colors duration-200 lg:text-sm text-xs"
                      >
                        <!-- <i class="fa fa-check-square"></i> -->
                        {{ allSelected ? "Deselect All" : "Select All" }}
                      </button>
                    </div>

                    <div :class="selectedItems.length === 0 ? 'hidden' : ''">
                      <button
                        @click="toggleDeleteMultiple"
                        class="flex items-center hover:bg-red-600 text-white px-3 lg:py-1 py-0.5 rounded-md transition-colors duration-200 disabled:opacity-50 whitespace-nowrap"
                        :disabled="selectedItems.length === 0"
                        :class="
                          selectedItems.length === 0
                            ? 'bg-pink-200'
                            : 'bg-red-500'
                        "
                      >
                        <i class="fa fa-trash mr-2"></i>
                        ({{ selectedItems.length }})
                      </button>
                    </div>
                  </div>

                  <div
                    class="flex items-center lg:my-0 my-2"
                    v-if="collegeFilterList"
                  >
                    <select
                      v-model="selectedCollege"
                      class="lg:px-3 px-1 lg:py-1.5 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 w-full text-xs"
                    >
                      <option value="">All Colleges</option>
                      <option
                        v-for="college in colleges"
                        :key="college.value"
                        :value="college.value"
                      >
                        {{ college.label }}
                      </option>
                    </select>
                  </div>

                  <div class="flex gap-x-2">
                    <button
                      @click="statusFilter = 'all'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'all'
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'bg-white text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white',
                      ]"
                    >
                      All
                    </button>
                    <button
                      @click="statusFilter = 'pending'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'pending'
                          ? 'bg-red-600 text-white border-red-600'
                          : 'bg-white text-red-600 border-red-600 hover:bg-red-600 hover:text-white',
                      ]"
                    >
                      Pending
                    </button>
                    <button
                      @click="statusFilter = 'done'"
                      :class="[
                        'px-2 py-1 rounded-xl border',
                        statusFilter === 'done'
                          ? 'bg-green-600 text-white border-green-600'
                          : 'bg-white text-green-600 border-green-600 hover:bg-green-600 hover:text-white',
                      ]"
                    >
                      Done
                    </button>
                  </div>

                  <div class="flex items-center w-full lg:mt-0 mt-2">
                    <div class="relative w-full">
                      <input
                        v-model="searchQuery"
                        type="search"
                        placeholder="Search"
                        class="lg:px-3 px-2 lg:py-1.5 py-1 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 w-full text-xs"
                        @keyup.enter="performSearch"
                      />
                      <button
                        @click="performSearch"
                        class="absolute right-0 top-0 h-full px-3 text-gray-500 hover:text-green-600"
                      >
                        <i class="fa fa-search"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="">
                <div class="appointment-lists mx-auto text-xs">
                  <div v-if="isLoading" class="text-center">
                    <div class="">
                      <div class="flex animate-pulse space-x-4">
                        <div class="flex-1">
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-200"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-200"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-300"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                          <div class="h-10 bg-gray-100"></div>
                          <div class="h-0.5 bg-gray-100"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else>
                    <div class="gap-4" v-if="paginatedListItems.length > 0">
                      <div
                        class="flex items-center h-auto shadow lg:mb-0 mb-3 border-gray-200"
                        v-for="(b, i) in filteredByCollege"
                        :key="i"
                      >
                        <div class="w-fit flex px-2">
                          <div class="">
                            <input
                              type="checkbox"
                              :value="b.id"
                              v-model="selectedItems"
                              class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                            />
                          </div>
                        </div>

                        <div
                          class="w-full px-2 py-1.5 border-l lg:flex items-center"
                          :class="i % 2 ? 'bg-gray-100' : ''"
                        >
                          <div class="w-full flex items-center">
                            <div class="w-full">
                              <div class="w-full lg:px-3 lg:flex items-center">
                                <div
                                  class="text-[10px] font-light whitespace-nowrap"
                                >
                                  <span class="mr-1">Tracking ID:</span>
                                  <span class="font-bold">
                                    {{ b.tracking_id }}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="w-full">
                            <div class="w-full my-1">
                              <div
                                class="w-full text-xs font-bold uppercase text-green-800 py-1 px-1 flex items-center"
                              >
                                <i class="fa fa-user mr-2.5"></i>
                                {{ b.lastname }}, {{ b.firstname }}
                                {{ b.middlename }}
                              </div>
                            </div>

                            <!-- Status Logs Modal -->
                            <div
                              v-if="showLogsModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closeLogsModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showLogsModal,
                                  'scale-95 opacity-0': !showLogsModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Status Logs -
                                    {{ currentItem?.tracking_id || "N/A" }}
                                  </h3>
                                  <button
                                    @click="closeLogsModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <!-- Logs List - Update to sort by timestamp -->
                                <div class="mb-6 max-h-[40vh] overflow-y-auto">
                                  <div
                                    v-if="
                                      !currentItem?.logs ||
                                      currentItem.logs.length === 0
                                    "
                                    class="text-center text-gray-500 py-4"
                                  >
                                    No logs available
                                  </div>

                                  <div v-else class="space-y-3">
                                    <div
                                      v-for="(log, index) in sortedLogs"
                                      :key="index"
                                      class="border-l-4 rounded-r text-[10px]"
                                      :class="
                                        index === sortedLogs.length - 1
                                          ? 'border-green-500 bg-green-50 py-3 '
                                          : 'border-gray-300 bg-gray-50'
                                      "
                                    >
                                      <div
                                        class="flex items-center justify-between capitalize gap-x-3"
                                      >
                                        <div
                                          class="font-medium w-9/12"
                                          :class="
                                            index === sortedLogs.length - 1
                                              ? 'text-green-800 text-sm'
                                              : 'text-gray-500'
                                          "
                                        >
                                          <div
                                            class="w-full bg-transparent focus:outline-none px-1"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.status_remarks }}
                                          </div>
                                        </div>
                                        <div class="text-gray-500 w-3/12">
                                          <div
                                            class="whitespace-nowrap bg-transparent text-center -mb-0.5 focus:outline-none px-1 pb-0.5"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.timestamp }}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Add New Log Form -->
                                <div class="border-t pt-4">
                                  <!-- <h4 class="font-medium text-gray-700 mb-2">Add New Status Update</h4> -->
                                  <div class="space-y-3">
                                    <div>
                                      <label
                                        for="default_status"
                                        class="block text-sm text-gray-700 mb-1"
                                        >Default Status</label
                                      >
                                      <select
                                        id="default_status"
                                        v-model="defaultStatus"
                                        @change="newLogRemarks = defaultStatus"
                                        class="w-full px-1 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                      >
                                        <option value="" selected>
                                          Choose Default Option
                                        </option>
                                        <option>
                                          Received. Now Processing
                                        </option>
                                        <option>Reviewed and Verified</option>
                                        <option>Assessment Paid</option>
                                        <option>For Pick Up or Sending</option>
                                        <option>Transaction Closed</option>
                                      </select>
                                    </div>

                                    <div>
                                      <label
                                        for="status_remarks"
                                        class="block text-sm text-gray-700 mb-1"
                                        >Status / Remarks</label
                                      >
                                      <textarea
                                        id="status_remarks"
                                        v-model="newLogRemarks"
                                        rows="1"
                                        class="w-full px-2 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                        placeholder="Enter status update..."
                                      ></textarea>
                                    </div>

                                    <div class="flex justify-end gap-2">
                                      <button
                                        @click="addNewLog"
                                        :disabled="!newLogRemarks.trim()"
                                        class="flex items-center px-5 py-1.5 justify-center rounded-md border border-transparent bg-green-600 text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                      >
                                        <i class="fa fa-plus mr-2 mt-0.5"></i>
                                        Add Status
                                      </button>
                                    </div>

                                    <!-- Add confirmation modal for email notification -->
                                    <Transition appear name="fade">
                                      <div
                                        v-if="showEmailConfirmation"
                                        class="fixed inset-0 z-50 overflow-y-auto"
                                      >
                                        <div
                                          class="flex min-h-full items-center justify-center p-4 text-center"
                                        >
                                          <div
                                            class="fixed inset-0 bg-black bg-opacity-25"
                                            @click="cancelEmailConfirmation"
                                          ></div>

                                          <div
                                            class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all"
                                          >
                                            <h3
                                              class="text-lg font-medium leading-6 text-gray-900"
                                            >
                                              Send Status Update Email
                                            </h3>
                                            <div class="mt-2">
                                              <p class="text-sm text-gray-500">
                                                Would you like to notify the
                                                applicant about this status
                                                update via email?
                                              </p>
                                              <div
                                                class="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4"
                                              >
                                                <div class="flex">
                                                  <div class="flex-shrink-0">
                                                    <i
                                                      class="fa fa-info-circle text-yellow-400"
                                                    ></i>
                                                  </div>
                                                  <div class="ml-3">
                                                    <p
                                                      class="text-sm text-yellow-700"
                                                    >
                                                      This will send an email to
                                                      <span
                                                        class="font-medium"
                                                        >{{
                                                          currentItem?.email
                                                        }}</span
                                                      >
                                                      with the latest status
                                                      information.
                                                    </p>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>

                                            <div
                                              class="mt-4 flex justify-end gap-2"
                                            >
                                              <button
                                                type="button"
                                                class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                                                @click="cancelEmailConfirmation"
                                              >
                                                No, Skip Email
                                              </button>
                                              <button
                                                type="button"
                                                class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                                                @click="confirmAndSendEmail"
                                                :disabled="isEmailSending"
                                              >
                                                <i
                                                  class="fa fa-paper-plane mr-2"
                                                  v-if="!isEmailSending"
                                                ></i>
                                                <i
                                                  class="fa fa-spinner fa-spin mr-2"
                                                  v-else
                                                ></i>
                                                {{
                                                  isEmailSending
                                                    ? "Sending..."
                                                    : "Yes, Send Email"
                                                }}
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </Transition>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Personal Info Modal -->
                            <div
                              v-if="
                                showPersonalInfoModal &&
                                currentPersonalInfo === b
                              "
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closePersonalInfoModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                                :class="{
                                  'scale-100 opacity-100':
                                    showPersonalInfoModal,
                                  'scale-95 opacity-0': !showPersonalInfoModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Personal Information
                                  </h3>
                                  <button
                                    @click="closePersonalInfoModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <div class="space-y-4">
                                  <!-- User Info -->
                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-user text-green-600"></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="text-sm font-medium">
                                        Fullname
                                      </p>
                                      <p class="text-xs shadow-md py-1 px-2">
                                        {{ b.firstname }} {{ b.middlename }}
                                        {{ b.lastname }}
                                      </p>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-bookmark text-green-600"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="text-sm font-medium">
                                        Tracking ID
                                      </p>
                                      <p class="text-xs shadow-md py-1 px-2">
                                        {{ b.tracking_id }}
                                      </p>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-envelope text-green-600"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Email
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.email"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-phone text-green-600"></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Contact Number
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.contact_number"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-birthday-cake text-green-500"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Date of Birth
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.birthdate"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-female text-green-500"
                                      ></i>
                                    </div>
                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Mother's Maiden Name
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.mother_maiden_name"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-graduation-cap text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        ICC / LSU Graduate?
                                      </p>
                                      <select
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.alumni"
                                      >
                                        <option value="yes">Alumnus</option>
                                        <option value="no">Non-Alumnus</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-university text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        College
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.college"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i class="fa fa-book text-green-500"></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Course
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.course"
                                      />
                                    </div>
                                  </div>

                                  <div class="flex items-center">
                                    <div
                                      class="w-10 h-10 rounded-full border shadow-md flex items-center justify-center"
                                    >
                                      <i
                                        class="fa fa-calendar text-green-500"
                                      ></i>
                                    </div>

                                    <div class="ml-3 text-sm w-full">
                                      <p class="font-medium text-gray-900">
                                        Academic Year Graduated or Attended
                                      </p>
                                      <input
                                        type="text"
                                        class="text-xs shadow-md py-1 px-2 w-full"
                                        v-model="b.year_graduated_last_attended"
                                      />
                                    </div>
                                  </div>

                                  <div class="w-full py-1">
                                    <div class="lg:flex items-center gap-x-3">
                                      <div
                                        class="font-bold text-[10px] mr-1 text-center"
                                      >
                                        IDs and Documents:
                                      </div>
                                      <div class="flex gap-x-3 w-fit mx-auto">
                                        <span class="flex gap-x-3" v-if="b">
                                          <a
                                            v-if="
                                              b.valid_id_front?.[0]?.url?.match(
                                                /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                              )
                                            "
                                            @click.prevent="
                                              openImageModal(
                                                b.valid_id_front[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              )
                                            "
                                            class="cursor-pointer rounded-full border-2 border-green-800"
                                          >
                                            <img
                                              :src="
                                                b.valid_id_front[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              "
                                              class="w-7 h-7 hover:opacity-80 rounded-full transition-opacity"
                                            />
                                          </a>

                                          <a
                                            v-if="
                                              b.valid_id_back?.[0]?.url?.match(
                                                /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                              )
                                            "
                                            @click.prevent="
                                              openImageModal(
                                                b.valid_id_back[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              )
                                            "
                                            class="cursor-pointer rounded-full border-2 border-green-800"
                                          >
                                            <img
                                              :src="
                                                b.valid_id_back[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              "
                                              class="w-7 h-7 hover:opacity-80 rounded-full transition-opacity"
                                            />
                                          </a>

                                          <a
                                            v-if="
                                              b.credential_evaluation_requests?.[0]?.url?.match(
                                                /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                              )
                                            "
                                            @click.prevent="
                                              openImageModal(
                                                b.credential_evaluation_requests[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              )
                                            "
                                            class="cursor-pointer rounded-full border-2 border-green-800"
                                          >
                                            <img
                                              :src="
                                                b.credential_evaluation_requests[0].url.match(
                                                  /https:\/\/.*?\.(jpg|jpeg|png|gif|pdf|doc|docx)/i
                                                )?.[0]
                                              "
                                              class="w-7 h-7 hover:opacity-80 rounded-full transition-opacity"
                                            />
                                          </a>
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!--Payment Info Modal -->

                            <div
                              v-if="showPaymentModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closePaymentModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showPaymentModal,
                                  'scale-95 opacity-0': !showPaymentModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Payment Information
                                  </h3>
                                  <button
                                    @click="closePaymentModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <div
                                  class="border-4 border-[#eeeeee] px-3 py-3 my-3 rounded-lg"
                                >
                                  <div class="flex w-full gap-x-3">
                                    <div class="flex items-center mb-2 w-full">
                                      <div class="text-sm w-full">
                                        <label class="text-xs text-gray-900"
                                          >Course</label
                                        >
                                        <input
                                          type="text"
                                          v-model="currentItemInfo.course"
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                        />
                                      </div>
                                    </div>

                                    <!-- Date Graduated/Last Attended -->
                                    <div class="flex items-center mb-2 w-full">
                                      <div class="text-sm w-full">
                                        <label class="text-xs text-gray-900"
                                          >Date Graduated/Last Attended</label
                                        >
                                        <input
                                          type="text"
                                          v-model="
                                            currentItemInfo.date_graduated_last_attended
                                          "
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                          placeholder="Month Day Year"
                                        />
                                      </div>
                                    </div>

                                    <!-- Total -->
                                    <div class="flex items-center mb-2">
                                      <div class="text-sm w-full">
                                        <label class="text-xs text-gray-900"
                                          >Total</label
                                        >
                                        <input
                                          type="number"
                                          v-model="currentItemInfo.total"
                                          class="w-full text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500"
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Detail Fees -->
                                  <div
                                    class="flex flex-col items-start w-fit mx-auto"
                                  >
                                    <div class="text-sm w-full">
                                      <p
                                        class="font-medium text-gray-900 text-center py-2"
                                      >
                                        Detail Fees
                                      </p>
                                      <div
                                        v-for="(
                                          fee, index
                                        ) in currentItemInfo?.detail_fees"
                                        :key="index"
                                        class="flex items-center space-x-2 mb-1"
                                      >
                                        <label
                                          :for="'fee_name_' + index"
                                          class="text-xs font-medium text-gray-700"
                                          >Fee Name:</label
                                        >
                                        <input
                                          type="text"
                                          :id="'fee_name_' + index"
                                          v-model="fee.fee_name"
                                          class="text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                                        />
                                        <label
                                          :for="'fee_amount_' + index"
                                          class="text-xs font-medium text-gray-700"
                                          >Amount:</label
                                        >
                                        <input
                                          type="number"
                                          :id="'fee_amount_' + index"
                                          v-model="fee.amount"
                                          class="text-xs shadow-md py-1 px-2 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500"
                                        />
                                        <button
                                          @click="removeDetailFee(index)"
                                          class="px-3 py-1 text-xs bg-red-500 text-white rounded-md hover:bg-red-600"
                                        >
                                          <i class="fa fa-close"></i>
                                        </button>
                                      </div>
                                    </div>
                                    <button
                                      @click="addDetailFee"
                                      class="mx-auto px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 mt-5"
                                    >
                                      Add Fee
                                    </button>
                                  </div>

                                  <!-- Submit Button -->
                                  <div class="mt-4 mx-auto w-fit block">
                                    <button
                                      @click="sendEmailDetailFee"
                                      class="px-5 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md"
                                    >
                                      Send Payment
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div
                              v-if="showLogsModal && currentItem === b"
                              class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                              @click="closeLogsModal"
                            >
                              <div
                                class="bg-white rounded-lg shadow-xl p-6 max-w-2xl w-full mx-4 transform transition-transform duration-300 max-h-[90vh] overflow-auto"
                                :class="{
                                  'scale-100 opacity-100': showLogsModal,
                                  'scale-95 opacity-0': !showLogsModal,
                                }"
                                @click.stop
                              >
                                <div
                                  class="flex justify-between items-center mb-4 border-b pb-3"
                                >
                                  <h3 class="text-lg font-medium text-gray-900">
                                    Status Logs -
                                    {{ currentItem?.tracking_id || "N/A" }}
                                  </h3>
                                  <button
                                    @click="closeLogsModal"
                                    class="text-gray-400 hover:text-gray-500"
                                  >
                                    <i class="fa fa-times"></i>
                                  </button>
                                </div>

                                <!-- Logs List - Update to sort by timestamp -->
                                <div class="mb-6 max-h-[40vh] overflow-y-auto">
                                  <div
                                    v-if="
                                      !currentItem?.logs ||
                                      currentItem.logs.length === 0
                                    "
                                    class="text-center text-gray-500 py-4"
                                  >
                                    No logs available
                                  </div>

                                  <div v-else class="space-y-3">
                                    <div
                                      v-for="(log, index) in sortedLogs"
                                      :key="index"
                                      class="border-l-4 rounded-r text-[10px]"
                                      :class="
                                        index === sortedLogs.length - 1
                                          ? 'border-green-500 bg-green-50 py-3 '
                                          : 'border-gray-300 bg-gray-50'
                                      "
                                    >
                                      <div
                                        class="flex items-center justify-between capitalize gap-x-3"
                                      >
                                        <div
                                          class="font-medium w-9/12"
                                          :class="
                                            index === sortedLogs.length - 1
                                              ? 'text-green-800 text-sm'
                                              : 'text-gray-500'
                                          "
                                        >
                                          <div
                                            class="w-full bg-transparent focus:outline-none px-1"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.status_remarks }}
                                          </div>
                                        </div>
                                        <div class="text-gray-500 w-3/12">
                                          <div
                                            class="whitespace-nowrap bg-transparent text-center -mb-0.5 focus:outline-none px-1 pb-0.5"
                                            :class="
                                              index === sortedLogs.length - 1
                                                ? 'border-green-300 focus:border-green-500'
                                                : 'border-gray-300 focus:border-gray-500'
                                            "
                                          >
                                            {{ log.timestamp }}
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Add New Log Form -->
                                <div class="border-t pt-4">
                                  <!-- <h4 class="font-medium text-gray-700 mb-2">Add New Status Update</h4> -->
                                  <div class="space-y-3">
                                    <div>
                                      <label
                                        for="default_status"
                                        class="block text-sm text-gray-700 mb-1"
                                        >Default Status</label
                                      >
                                      <select
                                        id="default_status"
                                        v-model="defaultStatus"
                                        @change="newLogRemarks = defaultStatus"
                                        class="w-full px-1 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                      >
                                        <option value="" selected>
                                          Choose Default Option
                                        </option>
                                        <option>
                                          Received. Now Processing
                                        </option>
                                        <option>Reviewed and Verified</option>
                                        <option>Assessment Paid</option>
                                        <option>For Pick Up or Sending</option>
                                        <option>Transaction Closed</option>
                                      </select>
                                    </div>

                                    <div>
                                      <label
                                        for="status_remarks"
                                        class="block text-sm text-gray-700 mb-1"
                                        >Status / Remarks</label
                                      >
                                      <textarea
                                        id="status_remarks"
                                        v-model="newLogRemarks"
                                        rows="1"
                                        class="w-full px-2 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                                        placeholder="Enter status update..."
                                      ></textarea>
                                    </div>

                                    <div class="flex justify-end gap-2">
                                      <button
                                        @click="addNewLog"
                                        :disabled="!newLogRemarks.trim()"
                                        class="flex items-center px-5 py-1.5 justify-center rounded-md border border-transparent bg-green-600 text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                      >
                                        <i class="fa fa-plus mr-2 mt-0.5"></i>
                                        Add Status
                                      </button>
                                    </div>

                                    <!-- Add confirmation modal for email notification -->
                                    <Transition appear name="fade">
                                      <div
                                        v-if="showEmailConfirmation"
                                        class="fixed inset-0 z-50 overflow-y-auto"
                                      >
                                        <div
                                          class="flex min-h-full items-center justify-center p-4 text-center"
                                        >
                                          <div
                                            class="fixed inset-0 bg-black bg-opacity-25"
                                            @click="cancelEmailConfirmation"
                                          ></div>

                                          <div
                                            class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all"
                                          >
                                            <h3
                                              class="text-lg font-medium leading-6 text-gray-900"
                                            >
                                              Send Status Update Email
                                            </h3>
                                            <div class="mt-2">
                                              <p class="text-sm text-gray-500">
                                                Would you like to notify the
                                                applicant about this status
                                                update via email?
                                              </p>
                                              <div
                                                class="mt-4 bg-yellow-50 border-l-4 border-yellow-400 p-4"
                                              >
                                                <div class="flex">
                                                  <div class="flex-shrink-0">
                                                    <i
                                                      class="fa fa-info-circle text-yellow-400"
                                                    ></i>
                                                  </div>
                                                  <div class="ml-3">
                                                    <p
                                                      class="text-sm text-yellow-700"
                                                    >
                                                      This will send an email to
                                                      <span
                                                        class="font-medium"
                                                        >{{
                                                          currentItem?.email
                                                        }}</span
                                                      >
                                                      with the latest status
                                                      information.
                                                    </p>
                                                  </div>
                                                </div>
                                              </div>
                                            </div>

                                            <div
                                              class="mt-4 flex justify-end gap-2"
                                            >
                                              <button
                                                type="button"
                                                class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2"
                                                @click="cancelEmailConfirmation"
                                              >
                                                No, Skip Email
                                              </button>
                                              <button
                                                type="button"
                                                class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                                                @click="confirmAndSendEmail"
                                                :disabled="isEmailSending"
                                              >
                                                <i
                                                  class="fa fa-paper-plane mr-2"
                                                  v-if="!isEmailSending"
                                                ></i>
                                                <i
                                                  class="fa fa-spinner fa-spin mr-2"
                                                  v-else
                                                ></i>
                                                {{
                                                  isEmailSending
                                                    ? "Sending..."
                                                    : "Yes, Send Email"
                                                }}
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </Transition>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="w-full">
                            <div
                              class="w-full lg:block flex items-center lg:py-0 py-2"
                              v-if="b.details"
                            >
                              <div
                                class="text-[10px] font-light whitespace-nowrap"
                              >
                                <span class="font-bold mr-1">Note:</span>
                                <span class=""> {{ b.details }}</span>
                              </div>
                            </div>

                            <div
                              class="lg:w-fit w-full flex items-center justify-center py-1 gap-x-3"
                            >
                              <button
                                @click="openPersonalInfoModal(b)"
                                class="w-full whitespace-nowrap text-green-700 cursor-pointer bg-white border shadow-lg border-green-500 hover:bg-green-800 px-5 pt-1 pb-1 hover:text-white font-bold rounded-full uppercase"
                              >
                                Verify
                              </button>
                              <button
                                class="w-full whitespace-nowrap text-green-700 cursor-pointer bg-white border shadow-lg border-green-500 hover:bg-green-800 px-5 pt-1 pb-1 hover:text-white font-bold rounded-full uppercase"
                                @click="openPaymentModal(b)"
                              >
                                Assess Payment
                              </button>

                              <button
                                class="w-full whitespace-nowrap text-green-700 cursor-pointer bg-white border shadow-lg border-green-500 hover:bg-green-800 px-5 pt-1 pb-1 hover:text-white font-bold rounded-full uppercase"
                              >
                                Check Payment
                              </button>
                              <button
                                class="w-full whitespace-nowrap text-green-700 cursor-pointer bg-white border shadow-lg border-green-500 hover:bg-green-800 px-5 pt-1 pb-1 hover:text-white font-bold rounded-full uppercase"
                              >
                                Release Document
                              </button>

                              <button
                                class="w-full whitespace-nowrap text-green-700 cursor-pointer bg-white border shadow-lg border-green-500 hover:bg-green-800 px-5 pt-1 pb-1 hover:text-white font-bold rounded-full uppercase"
                                @click="
                                  $nextTick(() => {
                                    if (!checkIfModalOpen()) {
                                      openLogsModal(b);
                                    }
                                  })
                                "
                              >
                                Status Logs ({{ b.logs ? b.logs.length : 0 }})
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Image Modal -->
                      <div
                        v-if="showImageModal"
                        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
                        @click="closeImageModal"
                      >
                        <div
                          class="relative max-w-4xl max-h-[90vh] overflow-auto bg-white p-2 rounded-lg"
                          @click.stop
                        >
                          <button
                            @click="closeImageModal"
                            class="absolute top-2 right-2 text-gray-700 hover:text-red-500 bg-white rounded-full w-8 h-8 flex items-center justify-center shadow-md"
                          >
                            <i class="fa fa-close"></i>
                          </button>
                          <img
                            :src="currentModalImage"
                            class="max-w-full max-h-[85vh] object-contain"
                          />
                        </div>
                      </div>

                      <!-- Improved Delete Confirmation Modal -->
                      <div
                        v-if="toggleConfirmDelete"
                        class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
                      >
                        <div
                          class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 transform transition-transform duration-300"
                          :class="{
                            'scale-100 opacity-100': toggleConfirmDelete,
                            'scale-95 opacity-0': !toggleConfirmDelete,
                          }"
                        >
                          <div class="text-center">
                            <div
                              class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4"
                            >
                              <i
                                class="fa fa-exclamation-triangle text-red-600 text-xl"
                              ></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">
                              Confirm Deletion
                            </h3>
                            <p class="text-sm text-gray-500 mb-6">
                              Are you sure you want to delete
                              {{
                                selectedItems.length === 1
                                  ? "this record"
                                  : "these " +
                                    selectedItems.length +
                                    " records"
                              }}? This action cannot be undone.
                            </p>
                          </div>
                          <div class="flex justify-center gap-4">
                            <button
                              @click="deleteItems"
                              class="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 transition-colors duration-200"
                              :disabled="isDeleting"
                            >
                              <i
                                class="fa fa-spinner fa-spin mr-2"
                                v-if="isDeleting"
                              ></i>
                              {{ isDeleting ? "Deleting..." : "Delete" }}
                            </button>
                            <button
                              @click="cancelDelete"
                              class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 transition-colors duration-200"
                              :disabled="isDeleting"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      class="flex justify-center my-4 pb-10"
                      v-if="filteredListItems.length > 0"
                    >
                      <button
                        :disabled="currentPage === 1"
                        @click="currentPage--"
                        class="px-2 py-2 mx-1 bg-gray-200 rounded hover:bg-gray-500 hover:text-white"
                      >
                        Prev
                      </button>

                      <button
                        v-for="page in visiblePages"
                        :key="page"
                        @click="currentPage = page"
                        class="hover:bg-green-500 hover:text-white"
                        :class="{
                          'px-4 py-2 mx-1 rounded': true,
                          'bg-green-800 text-white': currentPage === page,
                          'bg-gray-200': currentPage !== page,
                        }"
                      >
                        {{ page }}
                      </button>

                      <button
                        :disabled="currentPage === totalPages"
                        @click="currentPage++"
                        class="px-2 py-2 mx-1 bg-gray-200 rounded hover:bg-gray-500 hover:text-white"
                      >
                        Next
                      </button>
                    </div>

                    <div v-else-if="!isLoading" class="text-center my-5">
                      No items found!
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="fixed bottom-0 w-full">
      <DashboardFooter />
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
