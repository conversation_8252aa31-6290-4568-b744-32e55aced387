# Generated by Django 5.0.2 on 2025-04-08 04:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cits', '0007_lsuacademics'),
    ]

    operations = [
        migrations.CreateModel(
            name='UploadedFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('original_name', models.CharField(help_text='The original name of the uploaded file.', max_length=255)),
                ('google_drive_id', models.Char<PERSON>ield(blank=True, help_text='The ID of the file in Google Drive.', max_length=255, null=True)),
                ('google_drive_link', models.URLField(blank=True, help_text='The web view link to the file in Google Drive.', null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, help_text='The timestamp when the file was uploaded to our system.')),
                ('file_size', models.Integer<PERSON>ield(blank=True, help_text='The size of the uploaded file in bytes.', null=True)),
                ('content_type', models.CharField(blank=True, help_text='The MIME type of the uploaded file.', max_length=100, null=True)),
            ],
            options={
                'verbose_name': 'Uploaded File',
                'verbose_name_plural': 'Uploaded Files',
                'ordering': ['-uploaded_at'],
            },
        ),
    ]
