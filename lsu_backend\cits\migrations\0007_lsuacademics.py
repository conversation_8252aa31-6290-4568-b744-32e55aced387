# Generated by Django 5.0.2 on 2025-03-13 03:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cits', '0006_remove_hotlinenumber_email_address'),
    ]

    operations = [
        migrations.CreateModel(
            name='LSUAcademics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('description', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('logo', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('gallery', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('video', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('email', models.EmailField(max_length=255)),
                ('admin_level_role', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('created_at', models.DateTime<PERSON><PERSON>(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
