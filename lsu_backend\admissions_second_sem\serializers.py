from rest_framework import serializers
from .models import EnrolleeDataFirstSemModel, EnrolleeDataFirstSemNewStudentModel, EnrolleeDataFirstSemTransfereeStudentModel, EnrolleeDataFirstSemSecondDegreeHolderStudentModel, StudentProfileModel, EvaluationFormDataModel, ReceiptDataModel, AdmissionFormCompleteEmailNotificationDataModel, AdmissionsDoneEmailNotificationDataModel, AdvisingDoneEmailNotificationDataModel, AccountingDoneEmailNotificationDataModel, EvaluationDoneEmailNotificationDataModel

class EnrolleeDataFirstSemSerializer(serializers.ModelSerializer):
  class Meta:
    model = EnrolleeDataFirstSemModel
    fields = '__all__'

class EnrolleeDataFirstSemNewStudentSerializer(serializers.ModelSerializer):
  class Meta:
    model = EnrolleeDataFirstSemNewStudentModel
    fields = '__all__'

class EnrolleeDataFirstSemTransfereeStudentSerializer(serializers.ModelSerializer):
  class Meta:
    model = EnrolleeDataFirstSemTransfereeStudentModel
    fields = '__all__'

class EnrolleeDataFirstSemSecondDegreeHolderStudentSerializer(serializers.ModelSerializer):
  class Meta:
    model = EnrolleeDataFirstSemSecondDegreeHolderStudentModel
    fields = '__all__'

class StudentProfileSerializer(serializers.ModelSerializer):
  class Meta:
    model = StudentProfileModel
    fields = '__all__'

class EvaluationFormDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = EvaluationFormDataModel
    fields = '__all__'

class ReceiptDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = ReceiptDataModel
    fields = '__all__'

class AdmissionFormCompleteEmailNotificationDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = AdmissionFormCompleteEmailNotificationDataModel
    fields = '__all__'

class AdmissionsDoneEmailNotificationDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = AdmissionsDoneEmailNotificationDataModel
    fields = '__all__'

class AdvisingDoneEmailNotificationDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = AdvisingDoneEmailNotificationDataModel
    fields = '__all__'

class AccountingDoneEmailNotificationDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = AccountingDoneEmailNotificationDataModel
    fields = '__all__'

class EvaluationDoneEmailNotificationDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = EvaluationDoneEmailNotificationDataModel
    fields = '__all__'