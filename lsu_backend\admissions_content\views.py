from django.shortcuts import render
from rest_framework.response import Response
from rest_framework.views import APIView
from django.core.mail import send_mail
from django.utils.html import strip_tags
from rest_framework import viewsets
from django.conf import settings
from django.template import Context
from django.template.loader import render_to_string, get_template
from django.core.mail import EmailMessage, EmailMultiAlternatives
from email.mime.image import MIMEImage
from .forms import DocumentFilesForm, NewStudentForm, NewStudentEmailNotificationForm, AdmissionsDoneForm, AdvisingDoneForm, AccountingDoneForm, EvaluationDoneForm, EvaluationFormDataForm
from .models import NewStudentModel, DocumentFiles, NewStudentEmailNotification, AdmissionsDoneModel, AdvisingDoneModel, AccountingDoneModel, EvaluationDoneModel, EvaluationFormDataModel
from .serializers import NewStudentSerializer, DocumentFilesSerializer, NewStudentEmailNotificationSerializer, AdmissionsDoneSerializer, AdvisingDoneSerializer, AccountingDoneSerializer, EvaluationDoneSerializer, EvaluationFormDataSerializer

class SubmitNewStudentToGmail(APIView):
  def post(self, request):
    form = NewStudentEmailNotificationForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'LSU Admissions Form Completed'
      html_content = render_to_string('newstudent.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Admissions and Scholarships Center',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AdmissionsDone(APIView):
  def post(self, request):
    form = AdmissionsDoneForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Admissions Verified'
      html_content = render_to_string('admissionsdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AdvisingDone(APIView):
  def post(self, request):
    form = AdvisingDoneForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Course Advising Completed'
      html_content = render_to_string('advisingdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AccountingDone(APIView):
  def post(self, request):
    form = AccountingDoneForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Accounting Confirmation'
      html_content = render_to_string('accountingdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class EvaluationDone(APIView):
  def post(self, request):
    form = EvaluationDoneForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'LSU Enrollment Verified'
      html_content = render_to_string('evaluationdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class ListNewStudentsView(APIView):
  def get(self, request, format=None):
    obj = NewStudentModel.objects.all()
    serializer = NewStudentSerializer(obj, many=True)
    return Response(serializer.data)

class CreateNewStudentView(APIView):
  def post(self, request):
    form = NewStudentForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = NewStudentModel.objects.get(pk=pk)
    form = NewStudentForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = NewStudentModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class NewStudentsDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = NewStudentModel.objects.get(pk=pk)
    serializer = NewStudentSerializer(obj)
    return Response(serializer.data)

class ListEvaluationFormDataView(APIView):
  def get(self, request, format=None):
    obj = EvaluationFormDataModel.objects.all()
    serializer = EvaluationFormDataSerializer(obj, many=True)
    return Response(serializer.data)

class CreateEvaluationFormDataView(APIView):
  def post(self, request):
    form = EvaluationFormDataForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    form = EvaluationFormDataForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EvaluationFormDataDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    serializer = EvaluationFormDataSerializer(obj)
    return Response(serializer.data)

class DocumentFilesViewSet(viewsets.ModelViewSet):
  queryset = DocumentFiles.objects.all()
  serializer_class = DocumentFilesSerializer