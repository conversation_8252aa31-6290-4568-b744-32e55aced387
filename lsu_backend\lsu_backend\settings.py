from pathlib import Path
import os
import environ

# from pydo import Client

BASE_DIR = Path(__file__).resolve().parent.parent
env = environ.Env()
environ.Env.read_env() 

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['127.0.0.1']

USER_CREATE_PASSWORD_RETYPE = False

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    'djoser',
    'corsheaders',
    'django_jsonform',
    'admissions_content',
    'admissions_second_sem',
    'appointment',
    'schedule',
    'procurement_october',
    'cits',
    'library',
    'mco',
    'drs_two',
    'investiture_august',
    'landingPage',
    'humanResource',
    'django_extensions',
    'campus_pass_four',
    'registrar'
    # 'enrollment',
    # 'admissions',
    # 'downloads',
]

CORS_ALLOWED_ORIGINS = [
    "http://*************:3000",
    "https://lsu.edu.ph",
    "http://localhost:3000",
    "https://www.lsu.edu.ph"
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'lsu_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'lsu_backend.wsgi.application'



# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': os.environ.get('DB_NAME'),
#         'USER': os.environ.get('DB_USER'),
#         'PASSWORD': os.environ.get('DB_PASSWORD'),
#         'HOST': os.environ.get('DB_HOST'),
#         'PORT': os.environ.get('DB_PORT'),
#         'sslmode':'require', 
#     }
# }


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

STATIC_URL = 'static/'
MEDIA_URL = 'media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

AWS_ACCESS_KEY_ID = os.environ.get('DO_SPACES_KEY') #Get from your DO account
AWS_SECRET_ACCESS_KEY = os.environ.get('DO_SPACES_SECRET')
AWS_STORAGE_BUCKET_NAME = os.environ.get('DO_SPACES_NAME')
AWS_S3_ENDPOINT_URL = 'https://lsu-media-styles.sgp1.digitaloceanspaces.com'
AWS_S3_REGION_NAME = 'sgp1' # e.g., 'nyc3', 'sgp1'
AWS_DEFAULT_ACL = 'public-read'  # Or your desired ACL
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
MEDIA_URL = 'https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-media-styles/'

AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=*********',
}

REST_FRAMEWORK = {
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ]
}

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'aqfnjqktxuyzldzw'
DEFAULT_FROM_EMAIL = 'LSU <<EMAIL>>'
# EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')