from django.db import models
from django.template import defaultfilters
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.contrib.postgres.fields import ArrayField

class EnrolleeDataFirstSemModel(models.Model):
  RETURNEE_STUDENT = {
    'type' : 'object',
    'keys' : {
      'is_student_returnee' : {'type' : 'string'},
      'reason_dropping_out_college' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
      'question' : {'type' : 'string'},
      'reasons_for_returning_lsu' : {'type' : 'string'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.Char<PERSON>ield(max_length=255)
  lastname = models.Char<PERSON>ield(max_length=255)
  firstname = models.Char<PERSON><PERSON>(max_length=255)
  middlename = models.Char<PERSON><PERSON>(max_length=255)
  extension_or_suffix_name = models.Char<PERSON>ield(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_term_enrolled = models.CharField(max_length=255)
  last_academic_year_enrolled = models.CharField(max_length=255)
  alien_status_information_citizenship= models.CharField(max_length=255)
  alien_status_information_visa_status= models.CharField(max_length=255)
  alien_status_information_last_day_of_authorized_stay= models.CharField(max_length=255)
  alien_status_information_agent_name= models.CharField(max_length=255)
  alien_status_information_passport_number= models.CharField(max_length=255)
  alien_status_information_passport_place_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_acricard_date_issued= models.CharField(max_length=255)
  alien_status_information_acricard_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_crts_date_issued= models.CharField(max_length=255)
  alien_status_information_crts_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_ssp_date_issued= models.CharField(max_length=255)
  alien_status_information_ssp_date_of_expiry= models.CharField(max_length=255)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  shiftee_placement_details = models.CharField(max_length=255)
  shiftee_placement_department = models.CharField(max_length=255)
  shiftee_placement_recommendation_status = models.CharField(max_length=255)
  shiftee_placement_accepting_college = models.CharField(max_length=255)
  shiftee_placement_approval_status = models.CharField(max_length=255)
  enrollment_tracking_status_admissions_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_advising_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_accounting_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_validation_is_complete = models.CharField(max_length=255)
  returnee_student = JSONField(schema=RETURNEE_STUDENT, null=True)
  shiftee_student_is_student_shiftee = models.CharField(max_length=255)
  shiftee_student_college = models.CharField(max_length=255)
  shiftee_student_program = models.CharField(max_length=255)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id
 
class EnrolleeDataFirstSemNewStudentModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  senior_highschool_track = models.CharField(max_length=255)
  senior_highschool_strand = models.CharField(max_length=255)
  last_school_attended = models.CharField(max_length=255)
  shs_year_graduated = models.CharField(max_length=255)
  alien_status_information_citizenship= models.CharField(max_length=255)
  alien_status_information_visa_status= models.CharField(max_length=255)
  alien_status_information_last_day_of_authorized_stay= models.CharField(max_length=255)
  alien_status_information_agent_name= models.CharField(max_length=255)
  alien_status_information_passport_number= models.CharField(max_length=255)
  alien_status_information_passport_place_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_acricard_date_issued= models.CharField(max_length=255)
  alien_status_information_acricard_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_crts_date_issued= models.CharField(max_length=255)
  alien_status_information_crts_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_ssp_date_issued= models.CharField(max_length=255)
  alien_status_information_ssp_date_of_expiry= models.CharField(max_length=255)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status_admissions_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_advising_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_accounting_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_validation_is_complete = models.CharField(max_length=255)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  new_student_placement_details = models.CharField(max_length=255)
  new_student_placement_department = models.CharField(max_length=255)
  new_student_placement_test_administration_status = models.CharField(max_length=255)
  new_student_placement_counselor_student_encounter_status = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_portal_password = models.CharField(max_length=255)
  credential_student_lsu_email_username = models.CharField(max_length=255)
  credential_student_lsu_email_password = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_canvas_password = models.CharField(max_length=255)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class EnrolleeDataFirstSemTransfereeStudentModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_school_attended = models.CharField(max_length=255)
  last_term_enrolled = models.CharField(max_length=255)
  last_academic_year_enrolled = models.CharField(max_length=255)
  alien_status_information_citizenship= models.CharField(max_length=255)
  alien_status_information_visa_status= models.CharField(max_length=255)
  alien_status_information_last_day_of_authorized_stay= models.CharField(max_length=255)
  alien_status_information_agent_name= models.CharField(max_length=255)
  alien_status_information_passport_number= models.CharField(max_length=255)
  alien_status_information_passport_place_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_acricard_date_issued= models.CharField(max_length=255)
  alien_status_information_acricard_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_crts_date_issued= models.CharField(max_length=255)
  alien_status_information_crts_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_ssp_date_issued= models.CharField(max_length=255)
  alien_status_information_ssp_date_of_expiry= models.CharField(max_length=255)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status_admissions_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_advising_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_accounting_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_validation_is_complete = models.CharField(max_length=255)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  transferee_student_college = models.CharField(max_length=255)
  transferee_student_program = models.CharField(max_length=255)
  transferee_student_placement_details = models.CharField(max_length=255)
  transferee_student_placement_department = models.CharField(max_length=255)
  transferee_student_placement_test_administration_status = models.CharField(max_length=255)
  transferee_student_placement_counselor_student_encounter_status = models.CharField(max_length=255)
  transferee_student_placement_accepting_college = models.CharField(max_length=255)
  transferee_student_placement_approval_status = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_portal_password = models.CharField(max_length=255)
  credential_student_lsu_email_username = models.CharField(max_length=255)
  credential_student_lsu_email_password = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_canvas_password = models.CharField(max_length=255)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class EnrolleeDataFirstSemSecondDegreeHolderStudentModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_school_graduated = models.CharField(max_length=255)
  last_term_graduated = models.CharField(max_length=255)
  last_academic_year_graduated = models.CharField(max_length=255)
  alien_status_information_citizenship= models.CharField(max_length=255)
  alien_status_information_visa_status= models.CharField(max_length=255)
  alien_status_information_last_day_of_authorized_stay= models.CharField(max_length=255)
  alien_status_information_agent_name= models.CharField(max_length=255)
  alien_status_information_passport_number= models.CharField(max_length=255)
  alien_status_information_passport_place_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_issued= models.CharField(max_length=255)
  alien_status_information_passport_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_acricard_date_issued= models.CharField(max_length=255)
  alien_status_information_acricard_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_crts_date_issued= models.CharField(max_length=255)
  alien_status_information_crts_date_of_expiry= models.CharField(max_length=255)
  alien_status_information_ssp_date_issued= models.CharField(max_length=255)
  alien_status_information_ssp_date_of_expiry= models.CharField(max_length=255)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status_admissions_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_advising_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_accounting_is_complete = models.CharField(max_length=255)
  enrollment_tracking_status_validation_is_complete = models.CharField(max_length=255)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  second_degree_holder_student_college = models.CharField(max_length=255)
  second_degree_holder_student_program = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_portal_password = models.CharField(max_length=255)
  credential_student_lsu_email_username = models.CharField(max_length=255)
  credential_student_lsu_email_password = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_canvas_password = models.CharField(max_length=255)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class StudentProfileModel(models.Model):
  HOW_YOU_LEARN_ABOUT_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  REASONS_FOR_CHOOSING_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'question' : {'type' : 'string'},
        'list_items' : {
          'type' : 'array',
          'items' : {
            'type' : 'object',
            'keys' : {
              'label' : {'type' : 'string'},
              'value' : {'type' : 'boolean'},
              'description' : {'type' : 'string'},
            }
          }
        }
      }
    }
  }  
  STUDENT_EDUCATIONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'tab' : {
        'type' : 'array',
        'items' : {'type' : 'string'},
      },
      'details' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'education_level' : {'type' : 'string'},
            'name_of_previous_school' : {'type' : 'string'},
            'track_or_program' : {'type' : 'string'},
            'highest_honors_received' : {'type' : 'string'},
            'city_or_municipality_and_province' : {'type' : 'string'},
            'year_graduated_or_attended' : {'type' : 'string'},
          }
        }
      }
    }
  }
  primary_info_tracking_id = models.CharField(max_length=255)
  primary_info_student_lsu_id_number = models.CharField(max_length=255)
  primary_info_title = models.CharField(max_length=255)
  primary_info_lastname = models.CharField(max_length=255)
  primary_info_firstname = models.CharField(max_length=255)
  primary_info_middlename = models.CharField(max_length=255)
  primary_info_extension_or_suffix_name = models.CharField(max_length=255)
  primary_info_birth_sex = models.CharField(max_length=255)
  primary_info_birth_date = models.CharField(max_length=255)
  primary_info_birth_order = models.CharField(max_length=255)
  primary_info_birth_place = models.CharField(max_length=255)
  primary_info_religion = models.CharField(max_length=255)
  primary_info_citizenship = models.CharField(max_length=255)
  primary_info_civil_status = models.CharField(max_length=255)
  primary_info_nationality = models.CharField(max_length=255)
  primary_info_ethnicity = models.CharField(max_length=255)
  primary_info_college = models.CharField(max_length=255)
  primary_info_program = models.CharField(max_length=255)
  primary_info_contact_primary_number = models.CharField(max_length=255)
  primary_info_contact_alternate_number = models.CharField(max_length=255)
  primary_info_contact_personal_email_address = models.CharField(max_length=255)
  primary_info_contact_lsu_email_address = models.CharField(max_length=255)
  how_you_learn_about_lsu = JSONField(schema=HOW_YOU_LEARN_ABOUT_LSU, null=True)
  reasons_for_choosing_lsu = JSONField(schema=REASONS_FOR_CHOOSING_LSU, null=True)
  household_capacity_and_access_to_distance_learning = JSONField(schema=HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING, null=True)
  stud_tribal_or_indigenous_community_option = models.CharField(max_length=255)
  stud_tribal_or_indigenous_community_name = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_street_or_purok = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_barangay_or_village = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_city_or_municipality = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_zipcode = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_province_or_state = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_region = models.CharField(max_length=255)
  stud_cnt_info_perm_lvng_h_adr_ctgry_country = models.CharField(max_length=255)
  stud_cnt_info_the_same_address_question = models.CharField(max_length=255)
  stud_cnt_info_the_same_address_answer = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_street_or_purok = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_barangay_or_village = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_city_or_municipality = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_zipcode = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_province_or_state = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_region = models.CharField(max_length=255)
  stud_cnt_info_c_or_p_lvng_h_adr_ctgry_country = models.CharField(max_length=255)
  f_personal_info_vital_life_status = models.CharField(max_length=255)
  f_personal_info_lastname = models.CharField(max_length=255)
  f_personal_info_firstname = models.CharField(max_length=255)
  f_personal_info_middlename = models.CharField(max_length=255)
  f_personal_info_birth_date = models.CharField(max_length=255)
  f_personal_info_age = models.CharField(max_length=255)
  f_personal_info_civil_status = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_street_or_purok = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_barangay_or_village = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_city_or_municipality = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_zipcode = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_province_or_state = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_region = models.CharField(max_length=255)
  f_cont_info_perm_lvng_h_add_ctgry_country = models.CharField(max_length=255)   
  f_cont_info_the_same_adr_question = models.CharField(max_length=255)
  f_cont_info_the_same_adr_answer = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_street_or_purok = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_barangay_or_village = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_city_or_municipality = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_zipcode = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_province_or_state = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_region = models.CharField(max_length=255)
  f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_country = models.CharField(max_length=255)   
  f_contact_info_contact_number = models.CharField(max_length=255)
  f_contact_info_contact_email_address = models.CharField(max_length=255)
  f_emp_info_highest_education_completed = models.CharField(max_length=255)
  f_emp_info_occupation = models.CharField(max_length=255)
  f_emp_info_employment_status = models.CharField(max_length=255)
  f_emp_info_gross_monthly_income = models.CharField(max_length=255)
  f_emp_info_employer_or_company = models.CharField(max_length=255)
  m_personal_info_vital_life_status = models.CharField(max_length=255)
  m_personal_info_lastname = models.CharField(max_length=255)
  m_personal_info_firstname = models.CharField(max_length=255)
  m_personal_info_middlename = models.CharField(max_length=255)
  m_personal_info_birth_date = models.CharField(max_length=255)
  m_personal_info_age = models.CharField(max_length=255)
  m_personal_info_civil_status = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_street_or_purok = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_barangay_or_village = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_city_or_municipality = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_zipcode = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_province_or_state = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_region = models.CharField(max_length=255)
  m_contact_info_perm_lvng_home_add_ctgy_country = models.CharField(max_length=255)
  m_contact_info_the_same_address_question = models.CharField(max_length=255)
  m_contact_info_the_same_address_no = models.CharField(max_length=255) 
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_street_or_purok = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_barangay_or_village = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_city_or_municipality = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_zipcode = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_province_or_state = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_region = models.CharField(max_length=255)
  m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_country = models.CharField(max_length=255)
  m_cont_info_contact_number = models.CharField(max_length=255)
  m_cont_info_contact_email_address = models.CharField(max_length=255)  
  m_emp_info_highest_education_completed = models.CharField(max_length=255)
  m_emp_info_occupation = models.CharField(max_length=255)
  m_emp_info_employment_status = models.CharField(max_length=255)
  m_emp_info_gross_monthly_income = models.CharField(max_length=255)
  m_emp_info_employer_or_company = models.CharField(max_length=255)
  lg_prsnl_info_relation_to_student = models.CharField(max_length=255)
  lg_prsnl_info_lastname = models.CharField(max_length=255)
  lg_prsnl_info_firstname = models.CharField(max_length=255)
  lg_prsnl_info_middlename = models.CharField(max_length=255)
  lg_prsnl_info_birth_date = models.CharField(max_length=255)
  lg_prsnl_info_age = models.CharField(max_length=255)
  lg_prsnl_info_civil_status = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_street_or_purok = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_barangay_or_village = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_city_or_municipality = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_zipcode = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_province_or_state = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_region = models.CharField(max_length=255)
  lg_cont_info_perm_lvng_h_adr_ctgy_country = models.CharField(max_length=255)      
  lg_cont_info_the_same_address_question = models.CharField(max_length=255)
  lg_cont_info_the_same_address_answer = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_street_or_purok = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_barangay_or_village = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_city_or_municipality = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_zipcode = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_province_or_state = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_region = models.CharField(max_length=255)
  lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_country = models.CharField(max_length=255)
  lg_cont_info_contact_number = models.CharField(max_length=255)
  lg_cont_info_contact_email_address = models.CharField(max_length=255)
  lg_emp_info_highest_education_completed = models.CharField(max_length=255)
  lg_emp_info_occupation = models.CharField(max_length=255)
  lg_emp_info_employment_status = models.CharField(max_length=255)
  lg_emp_info_gross_monthly_income = models.CharField(max_length=255)
  lg_emp_info_employer_or_company = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_title = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_lastname = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_firstname = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_middlename = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_extension_or_suffix_name = models.CharField(max_length=255)
  stud_emrgcy_cont_info_p_info_relation_to_student = models.CharField(max_length=255) 
  stud_emrgcy_cont_info_adr_street_or_purok = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_barangay_or_village = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_city_or_municipality = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_zipcode = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_province_or_state = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_region = models.CharField(max_length=255)
  stud_emrgcy_cont_info_adr_country = models.CharField(max_length=255)
  stud_emrgcy_cont_info_cnt_primary_number = models.CharField(max_length=255)
  stud_emrgcy_cont_info_cnt_alternate_number = models.CharField(max_length=255)
  stud_emrgcy_cont_info_cnt_email_address = models.CharField(max_length=255)
  stud_choice_trck_prog_one_course_program = models.CharField(max_length=255)
  stud_choice_trck_prog_two_course_program = models.CharField(max_length=255)
  stud_choice_trck_prog_three_course_program = models.CharField(max_length=255)
  student_educational_info = JSONField(schema=STUDENT_EDUCATIONAL_INFO, null=True)
  student_education_info_num_learner_reference_number = models.CharField(max_length=255)
  student_education_info_num_ched_award_number = models.CharField(max_length=255)
  student_education_info_num_dswd_household_number = models.CharField(max_length=255)
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')

class EvaluationFormDataModel(models.Model):
  EVALUATION_FORM = {
    'type' : 'object',
    'keys' : {
      'main_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question_heading' : {'type' : 'string'},
            'question_list' : {
              'type' : 'array',
              'items' : {
                'type' : 'object',
                'keys' : {
                  'question' : {'type' : 'string'},
                  'score' : {
                    'type' : 'array',
                    'items' : {
                      'type' : 'object',
                      'keys' : {
                        'number' : {'type' : 'number'},
                        'text' : {'type' : 'string'},
                      }
                    }
                  },
                  'answer' : {'type' : 'string'},
                }
              }
            }
          }
        }
      },
      'sub_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question' : {'type' : 'string'},
            'answer' : {'type' : 'string'},
          }
        }
      }
    }
  }
  tracking_id = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  lsu_email_address = models.CharField(max_length=255)
  personal_email_address = models.CharField(max_length=255)
  evaluation_form = JSONField(schema=EVALUATION_FORM, null=True)

  def __str__(self):
    return self.tracking_id

class ReceiptDataModel(models.Model):
  # image = models.ImageField(upload_to='files/admissions/files/')
  image = models.FileField(upload_to='files/admissions/files/')
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')

class NewTrackingIDEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdmissionFormCompleteEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdmissionsDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdvisingDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AccountingDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class EvaluationDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class ValidationEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_portal_password = models.CharField(max_length=255)
  credential_student_lsu_email_username = models.CharField(max_length=255)
  credential_student_lsu_email_password = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_canvas_password = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id