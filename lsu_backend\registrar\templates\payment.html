<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Appointment Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333333;
      background-color: #ffffff;
      margin: 0;
      padding: 20px;
    }
    .container {
      border: 1px solid #e0e0e0;
      padding: 30px;
      border-radius: 8px;
      max-width: 600px;
      margin: auto;
      background-color: #f9f9f9;
    }
    .logo {
      text-align: center;
    }
    h2 {
      color: #006633;
      text-align: center;
      margin: 0 auto;
    }
    .section-title {
      font-weight: bold;
      margin-top: 20px;
      color: #004d26;
    }
    .footer {
      font-size: 0.9em;
      margin-top: 30px;
      border-top: 1px solid #cccccc;
      padding-top: 15px;
      color: #666666;
      text-align: center;
    }
    a {
      color: #006633;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">
      <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/lsu-c-v.png" alt="La Salle University Logo" width="200" />
    </div>

    <h2>Appointment Request Confirmation</h2>

    <p>Dear <strong>{{ firstname }} {{ lastname }}</strong>,</p>

    <p>Thank you for submitting your <strong>Higher Education Registrar Appointment Request</strong> at La Salle University – Ozamiz.</p>

    <p>We have received the following information:</p>

    <div class="section-title">Personal Information</div>
    <ul>
      <li><strong>Full Name:</strong> {{ firstname }} {{ middlename }} {{ lastname }}</li>
      <li><strong>Mother's Full Maiden Name:</strong> {{ mother_maiden_name }}</li>
      <li><strong>Contact Number:</strong> {{ contact_number }}</li>
      <li><strong>Email:</strong> {{ email }}</li>
      <li><strong>Date of Birth:</strong> {{ birthdate }}</li>
    </ul>

    <div class="section-title">Academic Background</div>
    <ul>
      <li><strong>Did you graduate in LCC/LSU?:</strong> {{ alumni }}</li>
      <li><strong>College:</strong> {{ college }}</li>
      <li><strong>Course:</strong> {{ course }}</li>
      <li><strong>Year Graduated or Last Attended:</strong> {{ year_graduated_last_attended }}</li>
    </ul>

    <div class="section-title">Request Details</div>
    <ul>
      <li><strong>Type of Document Request:</strong>
        {% if document_requests %}
          <ul>
            {% for doc in document_requests %}
              <li>{{ doc }}</li>
            {% endfor %}
          </ul>
        {% else %}
          <p>No document requests specified.</p>
        {% endif %}
      </li>
      {% if remarks %}
        <li><strong>Other Remarks:</strong> {{ remarks }}</li>
      {% endif %}
    </ul>

    <div class="section-title">Status Updates</div>
    {% if logs and logs|length > 0 %}
      <div style="border-left: 3px solid #006633; padding-left: 15px; margin-bottom: 20px;">
        {% for log in logs %}
          {% if log.status_remarks and log.timestamp %}
            {% if forloop.first %}
              <div style="margin-bottom: 10px; padding: 8px; background-color: #e6f2eb; border-radius: 4px;">
            {% else %}
              <div style="margin-bottom: 10px; padding: 8px;">
            {% endif %}
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                {% if latest_status and log.status_remarks == latest_status %}
                  <strong style="color: #006633; text-decoration: underline;">{{ log.status_remarks }} <span style="background-color: #ffeb3b; padding: 2px 5px; border-radius: 3px; font-size: 0.8em; color: #333;">NEW</span></strong>
                {% else %}
                  <strong style="color: #006633;">{{ log.status_remarks }}</strong>
                {% endif %}
                <span style="font-size: 0.85em; color: #666666;">{{ log.timestamp }}</span>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    {% else %}
      <p>No status updates available yet.</p>
    {% endif %}

    <div class="section-title">Uploaded Documents</div>
    
    <h4>Government ID (Front):</h4>
    {% if valid_id_front %}
      <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; justify-content: center;">
        {% for file in valid_id_front %}
          <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 250px; text-align: center;">
            <div style="height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
              <img src="{{ file.url }}" alt="{{ file.name }}" style="max-width: 100%; max-height: 200px; object-fit: contain;">
            </div>
            <!-- <div style="font-size: 12px; text-align: center; overflow: hidden; text-overflow: ellipsis;">
              <a href="{{ file.url }}" target="_blank">{{ file.name }}</a>
            </div> -->
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>No front ID files uploaded.</p>
    {% endif %}
    
    <h4>Government ID (Back):</h4>
    {% if valid_id_back %}
      <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; justify-content: center;">
        {% for file in valid_id_back %}
          <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 250px; text-align: center;">
            <div style="height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
              <img src="{{ file.url }}" alt="{{ file.name }}" style="max-width: 100%; max-height: 200px; object-fit: contain;">
            </div>
            <!-- <div style="font-size: 12px; text-align: center; overflow: hidden; text-overflow: ellipsis;">
              <a href="{{ file.url }}" target="_blank">{{ file.name }}</a>
            </div> -->
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>No back ID files uploaded.</p>
    {% endif %}
    
    <h4>Credential Evaluation Files:</h4>
    {% if credential_files %}
      <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 20px; justify-content: center;">
        {% for file in credential_files %}
          <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 250px; text-align: center;">
            <div style="height: 200px; display: flex; align-items: center; justify-content: center; margin-bottom: 5px;">
              <img src="{{ file.url }}" alt="{{ file.name }}" style="max-width: 100%; max-height: 200px; object-fit: contain;">
            </div>
            <!-- <div style="font-size: 12px; text-align: center; overflow: hidden; text-overflow: ellipsis;">
              <a href="{{ file.url }}" target="_blank">{{ file.name }}</a>
            </div> -->
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>No credential evaluation files uploaded.</p>
    {% endif %}

    <p>Our Registrar's Office will review your request and contact you shortly with further instructions.</p>

    <p>If you have any questions, please email us at <a href="mailto:<EMAIL>"><EMAIL></a> or call us at (088) 521-0841 local 106.</p>

    <div class="footer">
      La Salle University – Ozamiz<br>
      Barangay Aguada, Ozamiz City, Misamis Occidental, Philippines<br>
      📧 <a href="mailto:<EMAIL>"><EMAIL></a> | 🌐 <a href="https://www.lsu.edu.ph">www.lsu.edu.ph</a>
    </div>
  </div>
</body>
</html>
