from django.forms import ModelForm
from .models import EnrolleeDataFirstSemModel, EnrolleeDataFirstSemNewStudentModel, EnrolleeDataFirstSemTransfereeStudentModel, EnrolleeDataFirstSemSecondDegreeHolderStudentModel, StudentProfileModel, EvaluationFormDataModel, ReceiptDataModel, AdmissionFormCompleteEmailNotificationDataModel, AdmissionsDoneEmailNotificationDataModel, AdvisingDoneEmailNotificationDataModel, AccountingDoneEmailNotificationDataModel, EvaluationDoneEmailNotificationDataModel, ValidationEmailNotificationDataModel, NewTrackingIDEmailNotificationDataModel

class EnrolleeDataFirstSemForm(ModelForm):
  class Meta:
    model = EnrolleeDataFirstSemModel
    fields = '__all__'

class EnrolleeDataFirstSemNewStudentForm(ModelForm):
  class Meta:
    model = EnrolleeDataFirstSemNewStudentModel
    fields = '__all__'

class EnrolleeDataFirstSemTransfereeStudentForm(ModelForm):
  class Meta:
    model = EnrolleeDataFirstSemTransfereeStudentModel
    fields = '__all__'

class EnrolleeDataFirstSemSecondDegreeHolderStudentForm(ModelForm):
  class Meta:
    model = EnrolleeDataFirstSemSecondDegreeHolderStudentModel
    fields = '__all__'

class StudentProfileForm(ModelForm):
  class Meta:
    model = StudentProfileModel
    fields = '__all__'

class EvaluationFormDataForm(ModelForm):
  class Meta:
    model = EvaluationFormDataModel
    fields = '__all__'

class ReceiptDataForm(ModelForm):
  class Meta:
    model = ReceiptDataModel
    fields = '__all__'

class AdmissionFormCompleteEmailNotificationDataForm(ModelForm):
  class Meta:
    model = AdmissionFormCompleteEmailNotificationDataModel
    fields = '__all__'

class AdmissionsDoneEmailNotificationDataForm(ModelForm):
  class Meta:
    model = AdmissionsDoneEmailNotificationDataModel
    fields = '__all__'

class AdvisingDoneEmailNotificationDataForm(ModelForm):
  class Meta:
    model = AdvisingDoneEmailNotificationDataModel
    fields = '__all__'
  
class AccountingDoneEmailNotificationDataForm(ModelForm):
  class Meta:
    model = AccountingDoneEmailNotificationDataModel
    fields = '__all__'

class EvaluationDoneEmailNotificationDataForm(ModelForm):
  class Meta:
    model = EvaluationDoneEmailNotificationDataModel
    fields = '__all__'

class ValidationEmailNotificationDataForm(ModelForm):
  class Meta:
    model = ValidationEmailNotificationDataModel
    fields = '__all__'

class NewTrackingIDEmailNotificationDataForm(ModelForm):
  class Meta:
    model = NewTrackingIDEmailNotificationDataModel
    fields = '__all__'