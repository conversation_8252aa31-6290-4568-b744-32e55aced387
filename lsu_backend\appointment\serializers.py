from rest_framework import serializers

from .models import Appointment, Tracking, Image

class AppointmentSerializer(serializers.ModelSerializer):
  class Meta:
    model = Appointment
    fields = '__all__'
 
class AppointmentDetailSerializer(serializers.ModelSerializer):
  class Meta:
    model = Appointment
    fields = '__all__'

class TrackingSerializer(serializers.ModelSerializer):
  class Meta:
    model = Tracking
    fields = '__all__'
    
class TrackingDetailSerializer(serializers.ModelSerializer):
  class Meta:
    model = Tracking
    fields = '__all__'
    
class ImageSerializer(serializers.ModelSerializer):
  class Meta:
    model = Image
    fields = '__all__'