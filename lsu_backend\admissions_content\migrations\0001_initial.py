# Generated by Django 5.0.2 on 2025-01-21 05:27

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccountingDoneModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.Char<PERSON>ield(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='AdmissionsDoneModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.Char<PERSON><PERSON>(max_length=255)),
                ('student_lsu_id_number', models.Char<PERSON>ield(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='AdvisingDoneModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='DocumentFiles',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.FileField(upload_to='files/admissions/files/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='EvaluationDoneModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='EvaluationFormDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('lsu_email_address', models.CharField(max_length=255)),
                ('personal_email_address', models.CharField(max_length=255)),
                ('evaluation_form', django_jsonform.models.fields.JSONField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='NewStudentEmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('temporary_lsu_id_number', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='NewStudentModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('temporary_lsu_id_number', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('data_privacy_notice_consent', django_jsonform.models.fields.JSONField(null=True)),
                ('student_classification_status', django_jsonform.models.fields.JSONField(null=True)),
                ('preliminaries', django_jsonform.models.fields.JSONField(null=True)),
                ('admissions_list_filter', django_jsonform.models.fields.JSONField(null=True)),
                ('student_personal_info', django_jsonform.models.fields.JSONField(null=True)),
                ('student_tribal_or_indigenous_community', django_jsonform.models.fields.JSONField(null=True)),
                ('student_contact_info', django_jsonform.models.fields.JSONField(null=True)),
                ('alien_status_information', django_jsonform.models.fields.JSONField(null=True)),
                ('father_personal_info', django_jsonform.models.fields.JSONField(null=True)),
                ('father_contact_info', django_jsonform.models.fields.JSONField(null=True)),
                ('father_employment_info', django_jsonform.models.fields.JSONField(null=True)),
                ('mother_personal_info', django_jsonform.models.fields.JSONField(null=True)),
                ('mother_contact_info', django_jsonform.models.fields.JSONField(null=True)),
                ('mother_employment_info', django_jsonform.models.fields.JSONField(null=True)),
                ('legal_guardian_personal_info', django_jsonform.models.fields.JSONField(null=True)),
                ('legal_guardian_contact_info', django_jsonform.models.fields.JSONField(null=True)),
                ('legal_guardian_employment_info', django_jsonform.models.fields.JSONField(null=True)),
                ('student_emergency_contact_information', django_jsonform.models.fields.JSONField(null=True)),
                ('siblings', django_jsonform.models.fields.JSONField(null=True)),
                ('student_educational_info', django_jsonform.models.fields.JSONField(null=True)),
                ('returnee_student', django_jsonform.models.fields.JSONField(null=True)),
                ('student_education_information_number', django_jsonform.models.fields.JSONField(null=True)),
                ('student_choice', django_jsonform.models.fields.JSONField(null=True)),
                ('how_you_learn_about_lsu', django_jsonform.models.fields.JSONField(null=True)),
                ('reasons_for_choosing_lsu', django_jsonform.models.fields.JSONField(null=True)),
                ('household_capacity_and_access_to_distance_learning', django_jsonform.models.fields.JSONField(null=True)),
                ('has_health_condition', django_jsonform.models.fields.JSONField(null=True)),
                ('hereby_certification', django_jsonform.models.fields.JSONField(null=True)),
                ('media_release_consent', django_jsonform.models.fields.JSONField(null=True)),
                ('enrollment_tracking_status', django_jsonform.models.fields.JSONField(null=True)),
                ('receipt', django_jsonform.models.fields.JSONField(null=True)),
                ('evaluation', django_jsonform.models.fields.JSONField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
