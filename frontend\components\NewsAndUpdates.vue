<script setup>
const router = useRouter();
const displayMikrotik = ref(false);
const newsUpdates = ref([
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n1.jpg",
    link: "https://www.facebook.com/lsu.edu.ph/posts/pfbid02uqmtVSEBV2nu6qXkMcMJva1DbH5c3XXyvgu1qRZCRysNf2pFehnt3QPT2iGwUDzCl",
  },
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n2.png",
    link: "https://www.facebook.com/lsu.edu.ph/posts/pfbid0Cyv76mNwJA8DnyeuMnA45aQ6ahqr4vEYY3WfUvuSBgchJoYmkGmNVWaxoMvPUiTpl",
  },
  {
    image:
      "https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/newsupdates/n3.png",
    link: "https://www.facebook.com/lsu.edu.ph/posts/pfbid02maZUk8T7oLcsao1y5VF7ThDGKYiCfgDLhVFsUqhVTq8UCCw5acJEtJP1a87KQkdwl",
  },
]);

// Calendar slideshow
const calendarSlides = ref([
  {
    image:
      "https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/Monthly%20Calendar.png",
    caption: "Main Calendar",
  },
  {
    image:
      "https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/CalendarB.jpg",
    caption: "Alternate Calendar",
  },
  // {
  //   image: "https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/m3.png",
  //   caption: "March"
  // }
]);

const currentSlideIndex = ref(0);

const nextSlide = () => {
  currentSlideIndex.value =
    (currentSlideIndex.value + 1) % calendarSlides.value.length;
};

const prevSlide = () => {
  currentSlideIndex.value =
    currentSlideIndex.value === 0
      ? calendarSlides.value.length - 1
      : currentSlideIndex.value - 1;
};

const goToSlide = (index) => {
  currentSlideIndex.value = index;
};

// Auto-advance slides every 5 seconds
let slideInterval;
onMounted(() => {
  slideInterval = setInterval(nextSlide, 5000);
});

onUnmounted(() => {
  clearInterval(slideInterval);
});

const communityCalendar = () => {
  router.push("/calendar/community-calendar");
};

const alternativeCalendar = () => {
  router.push("/calendar/alternative");
};

const show = reactive({
  L: false,
  S: false,
  U: false,
});

function toggle(letter) {
  show[letter] = !show[letter];
}
</script>

<template>
  <div class="">
    <!-- <div class="bg-green-700">
      <div class="lg:pt-24 pt-5 lg:pb-20">
        <div class="w-11/12 mx-auto">
          <div
            class="mx-auto w-11/12 text-center font-bold lg:text-3xl text-lg uppercase text-white lg:mb-10"
          >
            News and Updates
          </div>
          <div
            class="lg:grid grid-cols-3 text-center mx-auto lg:py-10 pt-4 pb-2 gap-14 list-none"
          >
            <div
              class="hover:shadow-2xl hover:border hover:border-white w-full lg:h-[350px] h-[200px] relative lg:mb-0 mb-3 cursor-pointer shadow-4xl"
              v-for="(nu, i) in newsupdates"
              :key="i"
            >
              <NuxtLink :to="nu.link">
             
                <img :src="nu.image" class="object-cover h-full w-full z-10 absolute" />
                <button
                  class="text-left text-xs text-white w-full bg-black opacity-80 px-3 h-20 absolute bottom-0 left-1/2 transform -translate-x-1/2 z-20"
                >
                  
                  <div class="-mt-3">
                    <h1 class="text-sm font-bold">LSU Welcomes Sister Ann Carbon MSC</h1>
                    <h3 class="line-clamp-2 text-xs">
                      Welcome to La Salle University, Sister Anne Carbon MSC,
                      Congregational Leader of the Missionary Sisters of Saint Columban.
                    </h3>
                  </div>
                </button>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- <i class="fa fa-angle-down text-5xl" aria-hidden="true"></i> -->
    <!-- <div class="absolute w-full h-80" :style="{backgroundImage: 'url('+(nu.image)+')' , opacity: '0.2'}"></div>
                <div class="absolute w-full h-80 opacity-60"></div> -->
    <!-- <div
      class="w-fit lasalle-green-text rounded-md mx-auto text-center text-xl text-white font-bold p-3"
    >
      Higher Education
    </div> -->
    <!-- <div
      class="w-fit lasalle-green-text rounded-md mx-auto text-center text-xl text-white font-bold p-3"
    >
      Basic Education
    </div>
    <div
      class="grid grid-cols-3 text-center w-11/12 mx-auto py-5 gap-5 list-none"
    >
      <li class="border w-full h-44 bg-green-600">
        <img src="" class="" />
        <p class="text-center font-bold uppercase text-white">
          APPLICATION FOR ADMISSION
        </p>
      </li>
    </div> -->

    <!-- <div class="lg:block hidden">
      <div class="h-1 w-full bg-white">
      </div>
      <div class="h-2 w-full bg-green-700">
      </div>
      <div class="h-1 w-full bg-white">
      </div>
    </div>
    <div class="h-2 w-full bg-green-700">
    </div>
    <div class="h-1 w-full bg-white">
    </div> -->

    <div class="lg:flex">
      <div class="lg:bg-[#ffffff] relative w-full lg:pb-5">
        <div class="">
          <div class="lg:flex lg:mt-10 lg:w-11/12 mx-auto">
            <div class="w-full justify-center flex items-center">
              <!-- <a
                href="/about/educational-policy"
                class="w-fit flex hover:border-4 hover:border-green-700 mx-auto justify-center text-center"
              >
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/Educational%20Policy.png"
                  class="xl:w-auto lg:w-full lg:max-h-[600px] xl:max-h-[600px]"
                />
              </a> -->

              <div class="lg:pr-20">
                <div
                  class="w-full max-w-7xl bg-[#fff] shadow-md lg:rounded-lg overflow-hidden relative"
                >
                  <div class="">
                    <!-- Left Side: Header and Logo -->
                    <div
                      class="w-full lg:w-3/10 flex flex-col justify-center items-center lg:py-5 py-3 relative bg-gray-50 border-gray-200"
                    >
                      <div class="relative z-10 text-center">
                        <img
                          src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/lsu-c-v.png"
                          alt="La Salle University Logo"
                          class="lg:w-3/12 w-6/12 h-auto object-contain my-2 mx-auto"
                        />
                        <!-- <h1 class="text-[#008028] text-4xl font-medium my-2">La Salle University</h1> -->
                        <h2
                          class="text-[#008028] text-xl font-bold uppercase tracking-wider"
                        >
                          EDUCATION POLICY
                        </h2>
                      </div>
                      <!-- Background image overlay -->
                      <div
                        class="absolute inset-0 opacity-20 pointer-events-none"
                      ></div>
                    </div>

                    <!-- Right Side: Content -->
                    <div
                      class="w-full lg:w-7/10 px-5 lg:py-5 py-2 relative z-10 overflow-y-auto"
                    >
                      <!-- Introduction Paragraph -->
                      <p
                        class="lg:text-xs text-[10px] text-gray-800 mb-2 text-justify"
                      >
                        La Salle University stands at the forefront of
                        cultivating a vibrant learning community guided by
                        Gospel values within the region. Our active engagement
                        in societal transformation is driven by a commitment to
                        innovative education, rigorous research, and robust
                        community extension services, all rooted in the rich
                        tradition of Lasallian values. We tirelessly strive for
                        the betterment of society by imparting knowledge that
                        goes beyond the academic realm, including the
                        responsible management and dissemination of intellectual
                        property generated through research and innovation.
                      </p>

                      <div class="flex gap-x-4 mb-3">
                        <!-- L Section -->
                        <div
                          class="shadow-md text-center text-green-900 w-full cursor-pointer hover:bg-green-100"
                          @click="toggle('L')"
                        >
                          <div
                            class="lg:text-6xl text-3xl font-bold text-[#008028] leading-none flex items-start justify-center"
                          >
                            L
                          </div>
                          <i
                            class="fa fa-angle-double-down text-[10px] lg:-mt-1 mb-1 block text-gray-200"
                            aria-hidden="true"
                          ></i>
                        </div>

                        <!-- S Section -->
                        <div
                          class="shadow-md text-center text-green-900 w-full cursor-pointer hover:bg-green-100"
                          @click="toggle('S')"
                        >
                          <div
                            class="lg:text-6xl text-3xl font-bold text-[#008028] leading-none flex items-start justify-center"
                          >
                            S
                          </div>
                          <i
                            class="fa fa-angle-double-down text-[10px] lg:-mt-1 mb-1 block text-gray-200"
                            aria-hidden="true"
                          ></i>
                        </div>

                        <!-- U Section -->
                        <div
                          class="shadow-md text-center text-green-900 w-full cursor-pointer hover:bg-green-100"
                          @click="toggle('U')"
                        >
                          <div
                            class="lg:text-6xl text-3xl font-bold text-[#008028] leading-none flex items-start justify-center"
                          >
                            U
                          </div>
                          <i
                            class="fa fa-angle-double-down text-[10px] lg:-mt-1 mb-1 block text-gray-200"
                            aria-hidden="true"
                          ></i>
                        </div>
                      </div>

                      <div v-show="show.L" class="flex-1 mb-3">
                        <p class="text-xs text-[#008028] text-justify m-0">
                          <span class="font-bold">L</span>ifelong learning is
                          our passion. We are dedicated to delivering
                          outstanding learning experiences across all
                          educational levels. We prioritize learners' and
                          stakeholders' satisfaction while ensuring compliance
                          with relevant regulations.
                        </p>
                      </div>
                      <div v-show="show.S" class="flex-1 mb-3">
                        <p class="text-xs text-[#008028] text-justify m-0">
                          <span class="font-bold">S</span>ervice to the Almighty
                          and society is a calling. By integrating Gospel values
                          into our educational fabric, we aim to instill a
                          strong moral compass in our students, empowering them
                          to make meaningful contributions to their communities.
                          Our holistic and pragmatic approach, combining
                          academic rigor with a focus on societal
                          responsibility, positions La Salle University as a
                          trailblazer in shaping well-rounded individuals poised
                          to impact the world positively.
                        </p>
                      </div>

                      <div v-show="show.U" class="flex-1 mb-3">
                        <p class="text-xs text-[#008028] text-justify m-0">
                          <span class="font-bold">U</span>nwavering excellence
                          is our commitment. LSU strives to be abreast with the
                          appropriate scientific and technological developments,
                          adhere to the ethical conduct of educational research,
                          and support an inclusive and sustainable society. We
                          shall continually improve the efficiency of our
                          management systems, including the processes for
                          protecting and promoting intellectual property, as
                          well as the evaluation and expansion of community
                          extension programs, ensuring the consistent
                          advancement of the institution.
                        </p>
                      </div>

                      <!-- Footer with Document Info and Signature -->
                      <footer
                        class="flex flex-col md:flex-row justify-between items-start md:items-end mt-3 lg:pt-4 pt-2 border-t border-gray-200"
                      >
                        <div class="text-left lg:mt-4 mt-2 md:mt-0">
                          <p
                            class="text-[10px] font-bold text-[#008028] whitespace-nowrap"
                          >
                            (SGD) BR. REY E. MEJIAS FSC
                          </p>
                          <p class="text-xs text-gray-700">
                            University President
                          </p>
                        </div>
                        <div class="w-full">
                          <p
                            class="text-right text-gray-500 lg:text-[10px] text-[7px]"
                          >
                            ECM PO-01, REV. 01
                          </p>
                        </div>
                      </footer>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="w-full lg:w-10/12 lg:border-b-0 border-b lg:pb-0 pt-2 pb-2">
              <!-- <div
                class="text-center text-green-950 font-bold py-2 lg:bg-white bg-gray-100"
              >
                University Calendar
              </div> -->

              <div class="w-full">
                <div class="calendar-slideshow">
                  <!-- <h2 class="slideshow-caption">Community Calendar</h2> -->

                  <div class="slideshow-container shadow-2xl lg:border">
                    <div
                      v-for="(slide, index) in calendarSlides"
                      :key="index"
                      class="calendar-slide"
                      :class="{ active: index === currentSlideIndex }"
                    >
                      <img
                        :src="slide.image"
                        @click="communityCalendar"
                        class="slide-image"
                      />
                      <!-- <div class="slide-caption">{{ slide.caption }}</div> -->
                    </div>

                    <button class="prev-btn" @click="prevSlide">❮</button>
                    <button class="next-btn" @click="nextSlide">❯</button>
                  </div>

                  <div class="slide-dots">
                    <span
                      v-for="(slide, index) in calendarSlides"
                      :key="index"
                      class="dot"
                      :class="{ active: index === currentSlideIndex }"
                      @click="goToSlide(index)"
                    ></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="relative w-full lg:h-[400px] mt-20 hover:border-b-4 border-green-900 cursor-pointer lg:border-t-0 border-t-2" 
        @click="alternativeCalendar">
          <div class="bg-[#fff] w-full lg:h-[400px] z-10 lg:block hidden"></div>
          <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/CalendarB.jpg" 
          class="hover:opacity-70 lg:absolute lg:top-0 z-10 lg:h-[400px] lg:left-1/2 lg:transform lg:-translate-x-1/2"/>
        </div> -->

          <!-- <div class="lg:w-11/12 flex items-center lg:order-2 order-1"><img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/lsutingogbanner.jpg"
            class="object-cover w-full lg:h-full shadow-2xl max-h-[387px]"></div>
        <div class="lg:w-5/12 text-left lg:order-1 order-2 lg:px-10">
          <a href="https://www.facebook.com/lsutingog" target="_blank" class="mx-auto text-white w-full">
            <div class="lg:w-24 lg:h-38 lg:mb-5">
              <img
                src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/LSUTingogLogo.jpg"
                class="xl:h-44 h-32 object-cover lg:block hidden">
            </div>
            <div class="lg:bg-transparent lg:block flex bg-[#11590a] w-full items-center justify-between lg:px-0 px-3">
              <h1 class="lg:block lg:mb-10 flex italic font-semibold lg:text-white text-white"><span
                  class="uppercase block lg:text-xl lg:mt-9">tingog&nbsp;</span><span class="capitalize lg:text-xl">
                  student publications</span></h1>
              <button
                class="w-fit cursor-pointer lg:mb-10 my-2 text-left text-xs uppercase lg:text-green-900 text-white lg:bg-white 2xl:px-7 xl:px-4 px-3 lg:py-2.5 block"><span
                  class="lg:block hidden font-bold">learn more</span><span class="lg:hidden block"><i
                    class="fa fa-angle-double-right text-xl"></i></span></button>
            </div>
          </a>
        </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* Your existing styles... */

/* Calendar Slideshow Styles */
.calendar-slideshow {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.slideshow-caption {
  text-align: left;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  color: #333;
}

.slideshow-container {
  position: relative;
  width: 100%;
  height: 500px;
  // border: 1px solid #ddd;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.calendar-slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.6s ease;

  &.active {
    opacity: 1;
  }
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.slide-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  font-size: 1rem;
  width: auto;
}

.prev-btn,
.next-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 16px 10px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.prev-btn {
  left: 0;
  border-radius: 0 3px 3px 0;
}

.next-btn {
  right: 0;
  border-radius: 3px 0 0 3px;
}

.slide-dots {
  text-align: center;
  margin-top: 15px;
}

.dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin: 0 5px;
  background-color: #bbb;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s;

  &.active,
  &:hover {
    background-color: #006633;
  }
}

@media (max-width: 768px) {
  .slideshow-container {
    height: 350px;
  }

  .slideshow-caption {
    font-size: 1.2rem;
  }

  .prev-btn,
  .next-btn {
    padding: 12px 8px;
    font-size: 16px;
  }
}
</style>
