from django.db import models
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.template import defaultfilters
from storages.backends.s3boto3 import S3Boto3Storage  

class HEURegistrarAppointmentModel(models.Model):
    ITEMS_SCHEMA_FILES = {
        'type' : 'array',
        'items' : {
            'type' : 'object',
            'keys' : {
                'name' : {
                    'type' : 'string'
                },
                'url' : {
                    'type' : 'string'
                }
            }
        }
    }
    TYPE_DOC_REQ = {
        'type' : 'array',
        'items' : {
            'type' : 'string'
        }
    }
    STATUS_LOGS = {
        'type' : 'array',
        'items' : {
            'type' : 'object',
            'keys' : {
                'timestamp' : {
                    'type' : 'string'
                },
                'status_remarks' : {
                    'type' : 'string'
                }
            }
        }
    }
    firstname = models.CharField(max_length=255, blank=True, null=True, default='')
    middlename = models.CharField(max_length=255, blank=True, null=True, default='')
    lastname = models.Char<PERSON><PERSON>(max_length=255, blank=True, null=True, default='')
    birthdate = models.Char<PERSON><PERSON>(max_length=255, blank=True, null=True, default='')
    mother_maiden_name = models.Char<PERSON>ield(max_length=255, blank=True, null=True, default='')
    contact_number = models.Char<PERSON>ield(max_length=255, blank=True, null=True, default='')
    email = models.CharField(max_length=255, blank=True, null=True, default='')
    alumni = models.CharField(max_length=255, blank=True, null=True, default='')
    college = models.CharField(max_length=255, blank=True, null=True, default='')                            
    course = models.CharField(max_length=255, blank=True, null=True, default='')
    year_graduated_last_attended = models.CharField(max_length=255, blank=True, null=True, default='')
    type_document_requests = JSONField(schema=TYPE_DOC_REQ, null=True)
    details = models.CharField(max_length=255, blank=True, null=True, default='')
    valid_id_front = JSONField(schema=ITEMS_SCHEMA_FILES, null=True)
    valid_id_back = JSONField(schema=ITEMS_SCHEMA_FILES, null=True)
    credential_evaluation_requests = JSONField(schema=ITEMS_SCHEMA_FILES, null=True)
    tracking_id = models.CharField(max_length=255, blank=True, null=True, default='') 
    data_privacy = models.CharField(max_length=255, blank=True, null=True, default='')
    logs = JSONField(schema=STATUS_LOGS, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
  
    class Meta: 
        ordering = ('-created_at',)

    def created_at_formatted(self):
        return defaultfilters.date(self.created_at, 'M d, Y')
    
class RequestPaymentFeeModel(models.Model):
    ITEMS_SCHEMA_FILES = {
        'type' : 'array',
        'items' : {
            'type' : 'object',
            'keys' : {
                'fee_name' : {
                    'type' : 'string'
                },
                'amount' : {
                    'type' : 'string'
                }
            }
        }
    }
    payment_id = models.CharField(max_length=255, blank=True, null=True, default='')
    fullname = models.CharField(max_length=255, blank=True, null=True, default='')                           
    email = models.CharField(max_length=255, blank=True, null=True, default='')
    course = models.CharField(max_length=255, blank=True, null=True, default='')
    date_graduated_last_attended = models.CharField(max_length=255, blank=True, null=True, default='')
    total = models.CharField(max_length=255, blank=True, null=True, default='')
    detail_fees = JSONField(schema=ITEMS_SCHEMA_FILES, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
  
    class Meta: 
        ordering = ('-created_at',)

    def created_at_formatted(self):
        return defaultfilters.date(self.created_at, 'M d, Y')
    
class FileUploadModel(models.Model):
    file = models.FileField(storage=S3Boto3Storage(), upload_to='registrar/appointment/uploads/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file.name