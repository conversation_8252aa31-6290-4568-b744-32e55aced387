from django.db import models
from django.template import defaultfilters
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.contrib.postgres.fields import ArrayField

class NewStudentModel(models.Model):
  DATA_PRIVACY_NOTICE_CONSENT = {
    'type' : 'object',
    'keys' : {
      'read_above_statements_terms_and_conditions' : {'type' : 'boolean'},
      'is_eighteen_years_old_or_above' : {'type' : 'boolean'},
    }
  }
  STUDENT_CLASSIFICATION_STATUS = {
    'items' : {'type' : 'string'},
  }
  PRELIMINARIES = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'category' : {'type' : 'string'},
        'list' : {
          'type' : 'object',
          'keys' : {
            'student_classifications' : {'type' : 'string'},
            'descriptions' : {'type' : 'string'},
            'follow_up_questions_one' : {'type' : 'string'},
            'answers_one' : {'type' : 'string'},
            'follow_up_questions_two' : {'type' : 'string'},
          }
        }
      }
    }
  }
  ADMISSIONS_LIST_FILTER = {
    'type' : 'object',
    'keys' : {
      'senior_high_school_track' : {'type' : 'string'},
      'program_applied' : {'type' : 'string'},
      'college' : {'type' : 'string'},
      'new_term' : {'type' : 'string'},
      'new_academic_year' : {'type' : 'string'},
      'last_academic_year_enrolled' : {'type' : 'string'},
      'last_term_enrolled' : {'type' : 'string'},
      'name_of_previous_school' : {'type' : 'string'},
      'previous_program_enrolled' : {'type' : 'string'},
      'program' : {'type' : 'string'},
      'total_units_earned' : {'type' : 'string'},
    }
  }
  STUDENT_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'title' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'has_middlename' : {'type' : 'string'},
      'extension_or_suffix_name' : {'type' : 'string'},
      'birth_sex' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'birth_order' : {'type' : 'string'},
      'birth_place' : {'type' : 'string'},
      'religion' : {'type' : 'string'},
      'citizenship' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
      'nationality' : {'type' : 'string'},
      'ethnicity' : {'type' : 'string'},
    }
  }
  STUDENT_TRIBAL_OR_INDIGENOUS_COMMUNITY = {
    'type' : 'object',
    'keys' : {
      'option' : {'type' : 'string'},
      'name' : {'type' : 'string'},
    }
  }
  STUDENT_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'primary_number' : {'type' : 'string'},
          'alternate_number' : {'type' : 'string'},
          'personal_email_address' : {'type' : 'string'},
          'lsu_email_address' : {'type' : 'string'},
        }
      }
    }
  }
  ALIEN_STATUS_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'citizenship' : {'type' : 'string'},
      'visa_status' : {'type' : 'string'},
      'last_day_of_authorized_stay' : {'type' : 'string'},
      'agent_name' : {'type' : 'string'},
      'passport_number' : {'type' : 'string'},
      'passport_place_issued' : {'type' : 'string'},
      'passport_date_issued' : {'type' : 'string'},
      'passport_date_of_expiry' : {'type' : 'string'},
      'acricard_date_issued' : {'type' : 'string'},
      'acricard_date_of_expiry' : {'type' : 'string'},
      'crts_date_issued' : {'type' : 'string'},
      'crts_date_of_expiry' : {'type' : 'string'},
      'ssp_date_issued' : {'type' : 'string'},
      'ssp_date_of_expiry' : {'type' : 'string'},
    }
  }
  FATHER_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'vital_life_status' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  FATHER_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  FATHER_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  MOTHER_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'vital_life_status' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  MOTHER_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  MOTHER_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  LEGAL_GUARDIAN_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'relation_to_student' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  LEGAL_GUARDIAN_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  LEGAL_GUARDIAN_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  STUDENT_EMERGENCY_CONTACT_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'personal_info' : {
        'type' : 'object',
        'keys' : {
          'title' : {'type' : 'string'},
          'lastname' : {'type' : 'string'},
          'firstname' : {'type' : 'string'},
          'middlename' : {'type' : 'string'},
          'extension_or_suffix_name' : {'type' : 'string'},
          'relation_to_student' : {'type' : 'string'},
        }
      },
      'address' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'primary_number' : {'type' : 'string'},
          'alternate_number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  SIBLINGS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'lastname' : {'type' : 'string'},
        'firstname' : {'type' : 'string'},
        'middlename' : {'type' : 'string'},
        'birth_date' : {'type' : 'string'},
        'age' : {'type' : 'string'},
        'civil_status' : {'type' : 'string'},
        'grade_or_year_level' : {'type' : 'string'},
        'school_or_graduated_from' : {'type' : 'string'},
        'highest_education_completed' : {'type' : 'string'},
        'occupation' : {'type' : 'string'},
        'employer_or_company' : {'type' : 'string'},
      }
    }
  }
  STUDENT_EDUCATIONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'tab' : {
        'type' : 'array',
        'items' : {'type' : 'string'},
      },
      'details' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'education_level' : {'type' : 'string'},
            'name_of_previous_school' : {'type' : 'string'},
            'track_or_program' : {'type' : 'string'},
            'highest_honors_received' : {'type' : 'string'},
            'city_or_municipality_and_province' : {'type' : 'string'},
            'year_graduated_or_attended' : {'type' : 'string'},
          }
        }
      }
    }
  }
  RETURNEE_STUDENT = {
    'type' : 'object',
    'keys' : {
      'is_student_returnee' : {'type' : 'string'},
      'reason_dropping_out_college' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
      'question' : {'type' : 'string'},
      'reasons_for_returning_lsu' : {'type' : 'string'},
    }
  }
  STUDENT_EDUCATION_INFORMATION_NUMBER = {
    'type' : 'object',
    'keys' : {
      'learner_reference_number' : {'type' : 'string'},
      'ched_award_number' : {'type' : 'string'},
      'dswd_household_number' : {'type' : 'string'},
    }
  }
  STUDENT_CHOICE = {
    'type' : 'object',
    'keys' : {
      'choice_track_program_one_course_program' : {'type' : 'string'},
      'choice_track_program_two_course_program' : {'type' : 'string'},
      'choice_track_program_three_course_program' : {'type' : 'string'},
    }
  }
  HOW_YOU_LEARN_ABOUT_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  REASONS_FOR_CHOOSING_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'question' : {'type' : 'string'},
        'list_items' : {
          'type' : 'array',
          'items' : {
            'type' : 'object',
            'keys' : {
              'label' : {'type' : 'string'},
              'value' : {'type' : 'boolean'},
              'description' : {'type' : 'string'},
            }
          }
        }
      }
    }
  }
  HAS_HEALTH_CONDITION = {
    'type' : 'object',
    'keys' : {
      'answer' : {'type' : 'boolean'},
    }
  }
  HEREBY_CERTIFICATION = {
    'type' : 'object',
    'keys' : {
      'answer' : {'type' : 'boolean'},
    }
  }
  MEDIA_RELEASE_CONSENT = {
    'type' : 'object',
    'keys' : {
      'answer' : {'type' : 'string'},
    }
  }
  ENROLLMENT_TRACKING_STATUS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'track_name' : {'type' : 'string'},
        'label' : {'type' : 'string'},
        'details' : {'type' : 'string'},
        'date_checked' : {'type' : 'string'},
        'check_by' : {'type' : 'string'},
        'status' : {'type' : 'string'},
        'remarks' : {'type' : 'string'},
      }
    }
  }
  RECEIPT = {
    'type' : 'object',
    'keys' : {
      'image_url' : {'type' : 'string'},
      'submitted' : {'type' : 'boolean'},
      'confirm' : {'type' : 'boolean'},
    }
  }
  EVALUATION = {
    'type' : 'object',
    'keys' : {
      'submitted' : {'type' : 'boolean'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  data_privacy_notice_consent = JSONField(schema=DATA_PRIVACY_NOTICE_CONSENT, null=True)
  student_classification_status = JSONField(schema=STUDENT_CLASSIFICATION_STATUS, null=True)
  preliminaries = JSONField(schema=PRELIMINARIES, null=True)
  admissions_list_filter = JSONField(schema=ADMISSIONS_LIST_FILTER, null=True)
  student_personal_info = JSONField(schema=STUDENT_PERSONAL_INFO, null=True)
  student_tribal_or_indigenous_community = JSONField(schema=STUDENT_TRIBAL_OR_INDIGENOUS_COMMUNITY, null=True)
  student_contact_info = JSONField(schema=STUDENT_CONTACT_INFO, null=True)
  alien_status_information = JSONField(schema=ALIEN_STATUS_INFORMATION, null=True)
  father_personal_info = JSONField(schema=FATHER_PERSONAL_INFO, null=True)
  father_contact_info = JSONField(schema=FATHER_CONTACT_INFO, null=True)
  father_employment_info = JSONField(schema=FATHER_EMPLOYMENT_INFO, null=True)
  mother_personal_info = JSONField(schema=MOTHER_PERSONAL_INFO, null=True)
  mother_contact_info = JSONField(schema=MOTHER_CONTACT_INFO, null=True)
  mother_employment_info = JSONField(schema=MOTHER_EMPLOYMENT_INFO, null=True)
  legal_guardian_personal_info = JSONField(schema=LEGAL_GUARDIAN_PERSONAL_INFO, null=True)
  legal_guardian_contact_info = JSONField(schema=LEGAL_GUARDIAN_CONTACT_INFO, null=True)
  legal_guardian_employment_info = JSONField(schema=LEGAL_GUARDIAN_EMPLOYMENT_INFO, null=True)
  student_emergency_contact_information = JSONField(schema=STUDENT_EMERGENCY_CONTACT_INFORMATION, null=True)
  siblings = JSONField(schema=SIBLINGS, null=True)
  student_educational_info = JSONField(schema=STUDENT_EDUCATIONAL_INFO, null=True)
  returnee_student = JSONField(schema=RETURNEE_STUDENT, null=True)
  student_education_information_number = JSONField(schema=STUDENT_EDUCATION_INFORMATION_NUMBER, null=True)
  student_choice = JSONField(schema=STUDENT_CHOICE, null=True)
  how_you_learn_about_lsu = JSONField(schema=HOW_YOU_LEARN_ABOUT_LSU, null=True)
  reasons_for_choosing_lsu = JSONField(schema=REASONS_FOR_CHOOSING_LSU, null=True)
  household_capacity_and_access_to_distance_learning = JSONField(schema=HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING, null=True)
  has_health_condition = JSONField(schema=HAS_HEALTH_CONDITION, null=True)
  hereby_certification = JSONField(schema=HEREBY_CERTIFICATION, null=True)
  media_release_consent = JSONField(schema=MEDIA_RELEASE_CONSENT, null=True)
  enrollment_tracking_status = JSONField(schema=ENROLLMENT_TRACKING_STATUS, null=True)
  receipt = JSONField(schema=RECEIPT, null=True)
  evaluation = JSONField(schema=EVALUATION, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id
 
class EvaluationFormDataModel(models.Model):
  EVALUATION_FORM = {
    'type' : 'object',
    'keys' : {
      'main_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question_heading' : {'type' : 'string'},
            'question_list' : {
              'type' : 'array',
              'items' : {
                'type' : 'object',
                'keys' : {
                  'question' : {'type' : 'string'},
                  'score' : {
                    'type' : 'array',
                    'items' : {
                      'type' : 'object',
                      'keys' : {
                        'number' : {'type' : 'number'},
                        'text' : {'type' : 'string'},
                      }
                    }
                  },
                  'answer' : {'type' : 'string'},
                }
              }
            }
          }
        }
      },
      'sub_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question' : {'type' : 'string'},
            'answer' : {'type' : 'string'},
          }
        }
      }
    }
  }
  tracking_id = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  lsu_email_address = models.CharField(max_length=255)
  personal_email_address = models.CharField(max_length=255)
  evaluation_form = JSONField(schema=EVALUATION_FORM, null=True)

  def __str__(self):
    return self.tracking_id

class DocumentFiles(models.Model):
  # image = models.ImageField(upload_to='files/admissions/files/')
  image = models.FileField(upload_to='files/admissions/files/')
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')

class NewStudentEmailNotification(models.Model):
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdmissionsDoneModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdvisingDoneModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AccountingDoneModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class EvaluationDoneModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id