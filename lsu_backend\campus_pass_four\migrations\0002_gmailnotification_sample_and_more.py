# Generated by Django 5.0.2 on 2025-04-26 10:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('campus_pass_four', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incharge_firstname', models.Char<PERSON>ield(max_length=255)),
                ('incharge_contact_email', models.Char<PERSON><PERSON>(max_length=255)),
                ('schedule', models.Char<PERSON>ield(max_length=255)),
                ('approval_status', models.Char<PERSON><PERSON>(max_length=255)),
                ('remarks', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('tracking_id', models.Char<PERSON><PERSON>(max_length=255)),
                ('purpose', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Sample',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('incharge_firstname', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('incharge_contact_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.DeleteModel(
            name='CampusPassGmailNotificationPending',
        ),
    ]
