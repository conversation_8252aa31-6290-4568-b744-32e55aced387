# Generated by Django 5.0.2 on 2024-04-25 04:35

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cits', '0003_lsuothersocialmedia_social_media_logo_link'),
    ]

    operations = [
        migrations.CreateModel(
            name='CampusDirectory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('office', models.CharField(max_length=255)),
                ('location', models.CharField(max_length=255)),
                ('local_tel_no', django_jsonform.models.fields.JSONField(null=True)),
                ('mobile_no', django_jsonform.models.fields.JSONField(null=True)),
                ('email_address', django_jsonform.models.fields.JSONField(null=True)),
                ('campus', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='HotlineNumber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('office', models.CharField(max_length=255)),
                ('location', models.CharField(max_length=255)),
                ('label', models.CharField(max_length=255)),
                ('local_tel_no', django_jsonform.models.fields.JSONField(null=True)),
                ('mobile_no', django_jsonform.models.fields.JSONField(null=True)),
                ('email_address', django_jsonform.models.fields.JSONField(null=True)),
                ('campus', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
