# Generated by Django 5.0.2 on 2024-04-16 07:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cits', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LSUOtherSocialMedia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('social_media_name', models.Char<PERSON>ield(max_length=255)),
                ('social_media_link', models.Char<PERSON>ield(max_length=255)),
                ('category', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
