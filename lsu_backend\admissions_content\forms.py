from django.forms import ModelForm
from .models import NewStudentModel, DocumentFiles, NewStudentEmailNotification, AdmissionsDoneModel, AdvisingDoneModel, AccountingDoneModel, EvaluationDoneModel, EvaluationFormDataModel

class NewStudentForm(ModelForm):
  class Meta:
    model = NewStudentModel
    fields = '__all__'

class DocumentFilesForm(ModelForm):
  class Meta:
    model = DocumentFiles
    fields = '__all__'

class NewStudentEmailNotificationForm(ModelForm):
  class Meta:
    model = NewStudentEmailNotification
    fields = '__all__'

class AdmissionsDoneForm(ModelForm):
  class Meta:
    model = AdmissionsDoneModel
    fields = '__all__'

class AdvisingDoneForm(ModelForm):
  class Meta:
    model = AdvisingDoneModel
    fields = '__all__'

class AccountingDoneForm(ModelForm):
  class Meta:
    model = AccountingDoneModel
    fields = '__all__'

class EvaluationDoneForm(ModelForm):
  class Meta:
    model = EvaluationDoneModel
    fields = '__all__'

class EvaluationFormDataForm(ModelForm):
  class Meta:
    model = EvaluationFormDataModel
    fields = '__all__'