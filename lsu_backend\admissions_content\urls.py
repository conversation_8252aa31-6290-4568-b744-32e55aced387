from django.urls import path, include
from . import views 
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
# router.register('files', views.DocumentFilesViewSet, basename='files')
router.register('files', views.DocumentFilesViewSet)

urlpatterns = [
 path('file/list/', include(router.urls)),
 # path('file/list/files/', views.DocumentFilesViewSet.as_view()),
 path('submit-new-student-to-gmail/', views.SubmitNewStudentToGmail.as_view()),
 path('submit-new-student-to-gmail-admissions-done/', views.AdmissionsDone.as_view()),
 path('submit-new-student-to-gmail-advising-done/', views.AdvisingDone.as_view()),
 path('submit-new-student-to-gmail-accounting-done/', views.AccountingDone.as_view()),
 path('submit-new-student-to-gmail-evaluation-done/', views.EvaluationDone.as_view()),
 path('submit-evaluation-form/list/', views.ListEvaluationFormDataView.as_view()),
 path('submit-evaluation-form/create/', views.CreateEvaluationFormDataView.as_view()),
 path('submit-evaluation-form/<int:pk>/', views.EvaluationFormDataDetailView.as_view()),
 path('submit-evaluation-form/<int:pk>/delete/', views.CreateEvaluationFormDataView.as_view()),
 path('submit-evaluation-form/<int:pk>/edit/', views.CreateEvaluationFormDataView.as_view()),
 path('list/', views.ListNewStudentsView.as_view()),
 path('create/', views.CreateNewStudentView.as_view()),
 path('<int:pk>/', views.NewStudentsDetailView.as_view()),
 path('<int:pk>/delete/', views.CreateNewStudentView.as_view()),
 path('<int:pk>/edit/', views.CreateNewStudentView.as_view()),
]