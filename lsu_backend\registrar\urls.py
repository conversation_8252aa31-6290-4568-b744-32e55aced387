from django.urls import path

from . import views
from .views import FileUploadView

urlpatterns = [
    path('create/', views.RegistrarAppointmentCreateView.as_view()),
    path('list/', views.RegistrarAppointmentListView.as_view()),
    path('<int:pk>/', views.RegistrarAppointmentDetailView.as_view()),
    path('<int:pk>/delete/', views.RegistrarAppointmentCreateView.as_view()),
    path('<int:pk>/edit/', views.RegistrarAppointmentCreateView.as_view()),
    path('payment/create/', views.RegistrarPaymentFeeCreateView.as_view()),
    path('payment/list/', views.RegistrarPaymentFeeListView.as_view()),
    path('payment/<int:pk>/', views.RegistrarPaymentFeeDetailView.as_view()),
    path('payment/<int:pk>/delete/', views.RegistrarPaymentFeeCreateView.as_view()),
    path('payment/<int:pk>/edit/', views.RegistrarPaymentFeeCreateView.as_view()),
    path('upload/', FileUploadView.as_view(), name='file-upload'),
    path('submit-appointment-to-gmail/', views.SubmitAppointmentToGmail.as_view()),
    path('confirmation/', views.SendStatusUpdateEmail.as_view()),
    path('payment/fees/', views.SendRequestPaymentEmail.as_view()),
]
