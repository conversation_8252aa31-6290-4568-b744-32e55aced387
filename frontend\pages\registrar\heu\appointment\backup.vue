<script setup>
import { useUserStore } from "@/stores/user";
import _ from "lodash";
import VueDatePicker from "@vuepic/vue-datepicker";
//   import "./css/main.css";
import moment from "moment";
import { ref, computed, watch } from "vue";
import axios from "axios"; // For making HTTP requests
//   import headOfficeJSON from "./head_office.json";
//   const headOffice = ref(headOfficeJSON);

const userStore = useUserStore();
const endpoint = ref(userStore.mainDevServer);
const formDisplay = ref(true);
const thankYouDisplay = ref(false);
const submitCounter = ref(1);
const dateToday = moment().format("MMMM DD, YYYY h:mm:ss A");
const digitsNum = ref(0);

const requireAllFields = ref(false);

const info = ref({
  firstname: "",
  middlename: "",
  lastname: "",
  birthdate: "", 
  mother_maiden_name: "",
  contact_number: "",
  email: "",
  
  tracking_id: "",
  college: "",
  dateOfBirth: "",
  course: "",
  contactNumber: "",
  graduatedFromLSU: null, // Add this line
  documentRequests: [], // Add this line
  remarks: "", // Add this line
});

// Document request options as a dynamic array
const documentRequestOptions = ref([
  "Transcript of Records",
  "Transfer of Credentials (Honorable Dismissal)",
  "CAV (Certification, Authentication, Verification)",
  "Credential Evaluations (WES, CGFNS, NCLEX, SpanTran, IES, etc.)",
]);

// Document requests
const showOtherDocumentField = ref(false);
const otherDocumentRequest = ref("");
const documentRequestError = ref(false);

// Watch for changes to the "Other" checkbox and otherDocumentRequest
watch(
  [showOtherDocumentField, otherDocumentRequest],
  ([showField, otherValue]) => {
    // Remove any previous "Other: something" entries
    info.value.documentRequests = info.value.documentRequests.filter(
      (item) => !item.startsWith("Other:")
    );

    // Add the new "Other: something" entry if applicable
    if (showField && otherValue.trim()) {
      info.value.documentRequests.push(`Other: ${otherValue.trim()}`);
    }
  }
);

// Calculate min date (90 years ago from today)
const minBirthDate = computed(() => {
  const today = new Date();
  const minDate = new Date(today);
  minDate.setFullYear(today.getFullYear() - 90);
  return minDate.toISOString().split("T")[0]; // Format as YYYY-MM-DD
});

// Calculate max date (15 years ago from today)
const maxBirthDate = computed(() => {
  const today = new Date();
  const maxDate = new Date(today);
  maxDate.setFullYear(today.getFullYear() - 15);
  return maxDate.toISOString().split("T")[0]; // Format as YYYY-MM-DD
});

// Alternative implementation using moment.js if you prefer
// const minBirthDate = computed(() => moment().subtract(90, 'years').format('YYYY-MM-DD'));
// const maxBirthDate = computed(() => moment().subtract(15, 'years').format('YYYY-MM-DD'));

const updateReviewedByName = () => {
  const selectedOffice = headOffice.value.find(
    (office) => office.designation === info.value.originating_office
  );
  if (selectedOffice) {
    info.value.reviewed_by_name = selectedOffice.name;
    info.value.reviewed_by_designation = selectedOffice.offices;
    info.value.reviewed_by_email = selectedOffice.office_lsu_email;
  } else {
    info.value.reviewed_by_name = ""; // Clear if no match
  }
};

const invalidEmail = ref(false);

const validateEmail = () => {
  if (!info.value.email) return false;
  const pattern = /^[a-zA-Z0-9._-]+@(gmail\.com|lsu\.edu\.ph)$/;
  return pattern.test(info.value.email);
};

const invalidContactNumber = ref(false);

const validateContactNumber = () => {
  if (!info.value.contactNumber) return false;
  const pattern = /^(\+63|\+65)[0-9]{9,10}$/;
  return pattern.test(info.value.contactNumber);
};

// Check if all required fields are filled
const isFormValid = computed(() => {
  return (
    info.value.college &&
    info.value.course &&
    info.value.dateOfBirth &&
    info.value.contactNumber &&
    info.value.email &&
    info.value.graduatedFromLSU !== null &&
    info.value.graduatedFromLSU !== undefined &&
    validateContactNumber() &&
    validateEmail() &&
    info.value.documentRequests.length > 0 && // At least one document request
    (!showOtherDocumentField.value ||
      (showOtherDocumentField.value &&
        otherDocumentRequest.value.trim() !== "")) &&
    info.value.remarks // Remarks field is required
  );
});

const submitForm = async () => {
  if (!isFormValid.value) {
    requireAllFields.value = true;
    setTimeout(() => {
      requireAllFields.value = false;
    }, 3000);
    return;
  }

  let listItems =
    (await $fetch(endpoint.value + "/api/drs/list").catch(
      (error) => error.data
    )) || 0;
  if (listItems.length === 0) {
    info.value.tracking_id = "DRS-" + moment().year() + "-001";
    postAPI();
  } else if (info.value.originating_office === "Originating Office") {
    requireAllFields.value = true;
    setTimeout(() => {
      requireAllFields.value = false;
    }, 2000);
  } else {
    let filteredListItems = [];
    listItems.filter((params) => {
      filteredListItems.push(parseInt(params.tracking_id.slice(-3)));
    });
    let largestNumber = Math.max(...filteredListItems);
    console.log(largestNumber);
    digitsNum.value = largestNumber + 1;
    info.value.tracking_id =
      "DRS-" + moment().year() + "-" + String(digitsNum.value).padStart(3, "0");
    postAPI();
  }
};

const invalidLSUEmail = ref(false);
const postAPI = async () => {
  const pattern = /^[a-zA-Z0-9._-]+@lsu\.edu\.ph$/;

  if (!pattern.test(info.value.originating_email)) {
    invalidLSUEmail.value = true;

    setTimeout(() => {
      invalidLSUEmail.value = false;
    }, 3000);
  } else {
    if (submitCounter.value === 1) {
      submitCounter.value = 0;
      await $fetch(endpoint.value + "/api/drs/create/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: info.value,
      }).then((response) => {
        formDisplay.value = false;
        thankYouDisplay.value = true;
        submitCounter.value = 0;
        // console.log(response);
        submitDRSFormToGmail();
      });
    }
  }
};
const forRevisionInput = ref(false);
const changeStatus = () => {
  if (info.value.status === "For Revision") {
    forRevisionInput.value = true;
    // console.log(info.value.status)
  } else {
    forRevisionInput.value = false;
    // console.log(info.value.status)
  }
};

const submitDRSFormToGmail = async () => {
  await $fetch(endpoint.value + "/api/drs/drs-notification-submit/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: info.value,
  }).then((response) => {
    console.log(response);
  });
};

const collegeList = ref([
  "Arts and Sciences, Engineering, Architecture, Computer Studies",
  "Business-Related Courses, BSTM",
  "Nursing, and Graduate Studies",
  "Education Courses, BSHM",
  "Criminology",
]);

const courseList = ref([
  "Bachelor of Science in Business Administration (BSBA)",
  "Bachelor of Science in Accounting Information System (BSAIS)",
  "Bachelor of Science in Accountancy (BSAc)",
  "Bachelor of Science in Civil Engineering (BSCE)",
  "Bachelor of Science in Architecture (BSArch)",
  // Add more courses as needed
]);

const showAddNewCourse = ref(false);
const newCourse = ref("");

// Handle course selection change
const handleCourseChange = () => {
  if (info.value.course === "add_new") {
    showAddNewCourse.value = true;
    info.value.course = ""; // Clear the dropdown value
  }
};

// Add the new course to the list
const addNewCourse = () => {
  if (newCourse.value.trim()) {
    courseList.value.push(newCourse.value.trim());
    info.value.course = newCourse.value.trim();
    newCourse.value = "";
    showAddNewCourse.value = false;
  }
};

// Cancel adding new course
const cancelAddNew = () => {
  showAddNewCourse.value = false;
  newCourse.value = "";
};
</script>
<template>
  <div class="bg-gray-50">
    <Header />
    <div class="">
      <div class="relative">
        <div class="bg-green-700 h-[200px]">
          <div class="pt-10 absolute top-1/2 transform -translate-y-1/2 w-full">
            <h1
              class="font-bold uppercase text-white lg:text-2xl text-lg w-11/12 mx-auto"
            >
              REGISTRAR
            </h1>
            <p class="text-xs w-11/12 mx-auto text-white">
              Higher Education Registrar Appointment
            </p>
          </div>
        </div>

        <div class="shadow-lg text-green-700">
          <div class="lg:flex justify-between border-b border-gray-200 lg:pl-5">
            <div
              class="flex items-center capitalize text-xs lg:border-b-0 border-b lg:px-0 px-1.5 py-2"
            >
              <div>
                <a href="/" class="mr-2 hover:underline lg:h-10">Home</a>
              </div>
              <div>
                <i class="fas fa-caret-right"></i>
                <a href="/registrar" class="mx-2 hover:underline lg:h-10"
                  >HEU Appointment</a
                >
              </div>
            </div>
            <div class="flex hover:text-green-800 text-white bg-white h-full">
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
              >
                <a href="/registrar" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-video-camera" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Demo Guide</span>
                </a>
              </div>
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
              >
                <a href="/registrar" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-universal-access" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Track</span>
                </a>
              </div>
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full"
              >
                <a href="/registrar" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-user" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Admin Login</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="formDisplay" class="">
      <div
        class="header bg-gradient-to-b from-[#fefefe] via-[#fefefe] to-[#bce3c2] lg:pt-5 pt-1"
      >
        <div class="lg:w-9/12 w-11/12 mx-auto bg-white">
          <p class="text-green-900 text-xs font-bold text-center mb-2">
            Maayong La Salle!
          </p>
          <p class="text-green-900 text-xs text-center w-9/12 mx-auto">
            Please use this form in requesting documents at La Salle University
            Higher Education Registrar. By leveraging this form, graduates can
            conveniently request various documents online, eliminating the need
            for in-person appointments at LSU.
          </p>
          <form v-on:submit.prevent="submitForm" class="">
            <div class="border-2 border-green-700 shadow-lg my-3">
              <div class="">
                <h2
                  class="lg:text-base text-sm px-3 uppercase py-1.5 font-bold bg-green-900 text-white text-center tracking-wide"
                >
                  Request Appointment Form
                  <!-- <span class="font-light text-xs bg-green-900 text-white block">
                    {{ info.document_code }}</span> -->
                </h2>
                <!-- <div class="w-fit mx-auto text-xs mt-4 px-4 font-montserrat tracking-tight"> DRS No. <span class="border-b px-1">{{ info.tracking_id }}</span></div> -->
                <div class="lg:p-5 px-2 pt-3 pb-2 gap-3">
                  <div class="w-full lg:mb-0 mb-5">
                    <div class="w-full gap-3">
                      <div class="gap-3 w-full">
                        <div class="gap-3 lg:mb-2 shadow py-2 px-2">
                          <div class="lg:gap-x-2 gap-x-1 w-full">
                            <div class="flex gap-x-2">
                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Fullname
                                    <span
                                      class="text-red-600 font-normal text-sm">*</span>
                                  </div>
                                </label>
                                <div class="w-full flex gap-x-2">
                                  <input
                                    type="text"
                                    class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    placeholder="First Name"
                                    v-model="info.firstname"
                                    required
                                  />
                                  <input
                                    type="text"
                                    class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    placeholder="Middle Name"
                                    v-model="info.middlename"
                                    required
                                  />
                                  <input
                                    type="text"
                                    class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    placeholder="Last Name"
                                    v-model="info.lastname"
                                    required
                                  />
                                </div>
                              </div>

                              <div class="w-fit mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Date of Birth
                                    <span class="text-red-600 font-normal text-sm">*</span>
                                  </div>
                                </label>
                                <div
                                  class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                >
                                  <input
                                    type="date"
                                    class="px-1 w-full border-t-0 border-x-0 border-green-700 lg:h-[34px] h-8 text-xs"
                                    placeholder="Date of Birth"
                                    :min="minBirthDate"
                                    :max="maxBirthDate"
                                    v-model="info.birthdate"
                                    required
                                  />
                                </div>
                              </div>
                            </div>

                            <div class="flex gap-x-2">
                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Mother's Full Maiden Name
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div
                                  class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                >
                                  <input
                                    type="text"
                                    class="px-1 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                    placeholder="Mother's Full Maiden Name"
                                    v-model="info.mother_maiden_name"
                                    required
                                  />
                                </div>
                              </div>

                              <div class="lg:w-6/12 w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Contact Number
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div
                                  class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                >
                                  <input
                                    type="tel"
                                    class="px-2 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                    placeholder="e.g. +639210689089"
                                    v-model="info.contact_number"
                                    pattern="^(\+63|\+65)[0-9]{9,10}$"
                                    maxlength="13"
                                    title="Please enter a valid phone number with country code +63 or +65"
                                    required
                                  />
                                </div>
                                <p
                                  v-if="invalidContactNumber"
                                  class="text-xs text-red-700 mt-2 px-1"
                                >
                                  Please enter a valid phone number with country
                                  code +63
                                </p>
                              </div>

                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Email
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>

                                <div
                                  class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                >
                                  <!-- <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/Google_Drive.png" 
                                    class="h-5 w-5 ml-2" /> -->
                                  <!-- pattern="^[a-zA-Z0-9._-]+@(gmail\.com|lsu\.edu\.ph)$"
                                    title="Please enter a valid Gmail or LSU email address" -->
                                  <input
                                    type="email"
                                    class="px-2 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                    placeholder="e.g. <EMAIL>"
                                    v-model="info.email"
                                    required
                                    title="Please enter a valid Email Address"
                                  />
                                </div>
                                <!-- <p v-if="invalidEmail" class="text-xs text-red-700 mt-2 px-1">Only Gmail or LSU email addresses are accepted.</p> -->
                                <p
                                  v-if="invalidEmail"
                                  class="text-xs text-red-700 mt-2 px-1"
                                >
                                  Only Valid email addresses are accepted.
                                </p>
                              </div>
                            </div>

                            <div class="flex gap-x-2">
                              <div class="w-fit mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Did you graduated in ICC/LSU?
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div class="w-full">
                                  <div
                                    class="flex px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                  >
                                    <div class="w-fit mx-auto flex">
                                      <div
                                        class="flex gap-x-2 items-center w-[70px] justify-center"
                                      >
                                        <span>
                                          <input
                                            type="radio"
                                            value="yes"
                                            v-model="info.graduatedFromLSU"
                                            class="mr-1"
                                            required
                                            id="yes"
                                          />
                                        </span>
                                        <label
                                          class="text-sm hover:font-bold"
                                          for="yes"
                                        >
                                          Yes
                                        </label>
                                      </div>
                                      <div
                                        class="flex gap-x-2 items-center w-[70px] justify-center"
                                      >
                                        <span>
                                          <input
                                            type="radio"
                                            value="no"
                                            v-model="info.graduatedFromLSU"
                                            class="mr-1"
                                            required
                                            id="no"
                                          />
                                        </span>
                                        <label
                                          class="text-sm hover:font-bold"
                                          for="no"
                                        >
                                          No
                                        </label>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    College
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div class="w-full">
                                  <select
                                    v-model="info.college"
                                    class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    required
                                  >
                                    <option value="" disabled selected>
                                      Choose
                                    </option>
                                    <option
                                      v-for="(college, index) in collegeList"
                                      :key="index"
                                      :value="college"
                                    >
                                      {{ college }}
                                    </option>
                                  </select>
                                </div>
                              </div>
                            </div>

                            <div class="flex gap-x-2">
                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Course
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div class="w-full">
                                  <div v-if="!showAddNewCourse">
                                    <select
                                      v-model="info.course"
                                      @change="handleCourseChange"
                                      class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      required
                                    >
                                      <option value="" disabled selected>
                                        Choose
                                      </option>
                                      <option
                                        v-for="(course, index) in courseList"
                                        :key="index"
                                        :value="course"
                                      >
                                        {{ course }}
                                      </option>
                                      <option value="add_new">+ Other</option>
                                    </select>
                                  </div>
                                  <div v-else class="flex flex-col gap-2">
                                    <input
                                      v-model="newCourse"
                                      type="text"
                                      class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      placeholder="Enter new course"
                                      required
                                    />
                                    <div class="flex gap-2">
                                      <button
                                        @click="addNewCourse"
                                        class="text-xs bg-green-700 text-white px-2 py-1 rounded"
                                      >
                                        <i class="fa fa-check"></i>
                                      </button>
                                      <button
                                        @click="cancelAddNew"
                                        class="text-xs border border-green-700 text-green-700 px-2 py-1 rounded"
                                      >
                                        Cancel
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="w-fit mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Year Graduated or Last Attended
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div class="w-full">
                                  <select
                                    class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    required
                                  >
                                    <option value="" disabled selected>
                                      Select Year
                                    </option>
                                    <option
                                      v-for="year in _.range(
                                        moment().year(),
                                        1930,
                                        -1
                                      )"
                                      :key="year"
                                      :value="year"
                                    >
                                      {{ year }}
                                    </option>
                                  </select>
                                </div>
                              </div>
                            </div>

                            <div class="flex gap-x-2">
                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Type of Document Requests
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div
                                  class="w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm p-2"
                                >
                                  <div class="space-y-2">
                                    <div
                                      v-for="(
                                        document, index
                                      ) in documentRequestOptions"
                                      :key="index"
                                      class="flex items-center"
                                    >
                                      <input
                                        type="checkbox"
                                        :id="'doc_' + index"
                                        :value="document"
                                        v-model="info.documentRequests"
                                        class="mr-2"
                                      />
                                      <label
                                        :for="'doc_' + index"
                                        class="text-xs"
                                        >{{ document }}</label
                                      >
                                    </div>

                                    <div class="flex items-center">
                                      <input
                                        type="checkbox"
                                        id="other_doc"
                                        value="other"
                                        v-model="showOtherDocumentField"
                                        class="mr-2"
                                      />
                                      <label for="other_doc" class="text-xs"
                                        >Other:</label
                                      >

                                      <input
                                        v-if="showOtherDocumentField"
                                        type="text"
                                        v-model="otherDocumentRequest"
                                        class="ml-2 px-2 py-1 border-b border-green-700 bg-transparent text-xs w-full"
                                        placeholder="Please specify"
                                      />
                                    </div>
                                  </div>

                                  <p
                                    v-if="documentRequestError"
                                    class="text-xs text-red-700 mt-2"
                                  >
                                    Please select at least one document type
                                  </p>
                                </div>
                              </div>

                              <div class="w-full mb-2">
                                <label
                                  class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                >
                                  <div class="">
                                    Other Remarks
                                    <span
                                      class="text-red-600 font-normal text-sm"
                                      >*</span
                                    >
                                  </div>
                                </label>
                                <div class="w-full">
                                  <textarea
                                    class="px-2 py-2 box-border w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm text-xs"
                                    placeholder="Remarks"
                                    v-model="info.remarks"
                                    rows="7"
                                    cols="50"
                                    required
                                  ></textarea>
                                </div>
                              </div>
                            </div>

                            <div class="mt-7 mb-4 bg-gray-50">
                              <div
                                class="text-center text-green-900 font-bold uppercase mb-3 pt-3 flex items-center justify-center"
                              >
                                <i
                                  class="fa fa-file mr-2"
                                  aria-hidden="true"
                                ></i>
                                Upload Documents
                              </div>
                              <div class="flex gap-x-2 py-10 px-5 bg-[#fafafa]">
                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Government Issue ID (Front)
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <input
                                      type="file"
                                      class="px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    />
                                  </div>
                                </div>

                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Government Issue ID (Back)
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <input
                                      type="file"
                                      class="px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    />
                                  </div>
                                </div>

                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Credential Evaluations Requests
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <input
                                      type="file"
                                      class="px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="requireAllFields"
                  class="my-10 w-11/12 mx-auto text-white bg-red-800 text-center py-2 px-5 block lg:text-sm text-xs"
                >
                  All fields are required!
                </div>
                <div class="pb-5 lg:px-5 px-3 mb-1">
                  <button
                    type="submit"
                    :disabled="!isFormValid"
                    @click.prevent="submitForm"
                    class="px-10 lg:rounded-lg rounded-md bg-green-900 text-white font-bold py-1.5 lg:w-fit w-full mx-auto block uppercase hover:bg-white border-2 border-green-900 hover:text-green-900 lg:text-sm text-xs disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-green-900 disabled:hover:text-white"
                  >
                    <i class="fa fa-paper-plane mr-2" aria-hidden="true"></i>
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
        <!--Waves Container-->
        <div>
          <svg
            class="waves"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 24 150 28"
            preserveAspectRatio="none"
            shape-rendering="auto"
          >
            <defs>
              <path
                id="gentle-wave"
                d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z"
              />
            </defs>
            <g class="parallax">
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="0"
                fill="rgba(255,255,255,0.7"
              />
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="3"
                fill="rgba(255,255,255,0.5)"
              />
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="5"
                fill="rgba(255,255,255,0.3)"
              />
              <use xlink:href="#gentle-wave" x="48" y="7" fill="#fff" />
            </g>
          </svg>
        </div>
        <!--Waves end-->
      </div>
    </div>
    <div v-if="thankYouDisplay" class="">
      <div
        class="lg:flex gap-10 lg:rounded-4xl bg-white lg:px-14 px-3 py-1 lg:w-fit w-full mx-auto lg:my-10 shadow-sm"
      >
        <div class="flex items-center">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/icons/check-mark-icon-isolated-on-white-background-vector-26464923.jpg"
            class="lg:w-44 w-20 mx-auto lg:mt-0 mt-14"
          />
        </div>
        <div
          class="text-xl text-green-900 text-center w-fit mx-auto lg:py-20 py-5"
        >
          <h1 class="font-bold text-3xl">Thanks for submitting!</h1>
          <p class="font-light pt-3 pb-10">Your request has been sent!</p>
          <p class="font-light text-xs italic mb-10">
            Please check your email.
          </p>
          <a
            href="https://lsu.edu.ph/registrar"
            class="bg-green-800 text-white rounded-3xl py-1.5 px-10 lg:mb-0 mb-5 mx-auto w-fit lg:block hidden text-sm uppercase"
          >
            <i class="fa fa-arrow-circle-left mr-4"></i> Registrar
          </a>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
<style scoped>
input[type="radio"] {
  margin: 3px auto auto auto;
}

.error {
  color: red;
}

.waves {
  position: relative;
  width: 100%;
  height: 15vh;
  margin-bottom: -7px;
  /*Fix for safari gap*/
  min-height: 100px;
  max-height: 150px;
}

/* Animation */
.parallax > use {
  animation: move-forever 25s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
}

.parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}

.parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}

.parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}

.parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}

@keyframes move-forever {
  0% {
    transform: translate3d(-90px, 0, 0);
  }

  100% {
    transform: translate3d(85px, 0, 0);
  }
}

/*Shrinking for mobile*/
@media (max-width: 768px) {
  .waves {
    height: 40px;
    min-height: 40px;
  }

  .content {
    height: 30vh;
  }

  h1 {
    font-size: 24px;
  }
}
</style>
