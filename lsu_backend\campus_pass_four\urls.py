from django.urls import path
from . import views

urlpatterns = [
    path('list/', views.ListView.as_view()),
    path('<int:pk>/', views.DetailView.as_view()),
    path('<int:pk>/delete/', views.CreateView.as_view()),
    path('<int:pk>/edit/', views.CreateView.as_view()),
    path('create/', views.CreateView.as_view()),
    path('to-gmail-pending/', views.SubmitAppointmentToGmailPending.as_view()),
    path('to-gmail-declined/', views.SubmitAppointmentToGmailDeclined.as_view()),
    path('to-gmail-approved/', views.SubmitAppointmentToGmailApproved.as_view()),
    path('to-gmail-for-revision/', views.SubmitAppointmentToGmailForRevision.as_view()),
]