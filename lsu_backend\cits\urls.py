from django.urls import path
from . import views

urlpatterns = [
    path('lsu-partner-gmail/list/', views.ListLSUPartnerGMAILView.as_view()),
    path('lsu-partner-gmail/create/', views.CreateLSUPartnerGMAILView.as_view()),
    path('lsu-partner-gmail/<int:pk>/', views.LSUPartnerGMAILDetailView.as_view()),
    path('lsu-partner-gmail/<int:pk>/edit/', views.CreateLSUPartnerGMAILView.as_view()),
    path('lsu-partner-gmail/<int:pk>/delete/', views.CreateLSUPartnerGMAILView.as_view()),
    
    path('lsu-download-file/list/', views.ListLSUDownloadFileView.as_view()),
    path('lsu-download-file/create/', views.CreateLSUDownloadFileView.as_view()),
    path('lsu-download-file/<int:pk>/', views.LSUDownloadFileDetailView.as_view()),
    path('lsu-download-file/<int:pk>/edit/', views.CreateLSUDownloadFileView.as_view()),
    path('lsu-download-file/<int:pk>/delete/', views.CreateLSUDownloadFileView.as_view()),

    path('directory/list/', views.ListDirectoryView.as_view()),
    path('directory/create/', views.CreateDirectoryView.as_view()),
    path('directory/<int:pk>/', views.DirectoryDetailView.as_view()),
    path('directory/<int:pk>/edit/', views.CreateDirectoryView.as_view()),
    path('directory/<int:pk>/delete/', views.CreateDirectoryView.as_view()),
    
    path('lsu-volume-file/list/', views.ListLSUVolumeFileView.as_view()),
    path('lsu-volume-file/create/', views.CreateLSUVolumeFileView.as_view()),
    path('lsu-volume-file/<int:pk>/', views.LSUVolumeFileDetailView.as_view()),
    path('lsu-volume-file/<int:pk>/edit/', views.CreateLSUVolumeFileView.as_view()),
    path('lsu-volume-file/<int:pk>/delete/', views.CreateLSUVolumeFileView.as_view()),
    
    path('lsu-social-media-links/list/', views.ListLSUsmlFileView.as_view()),
    path('lsu-social-media-links/create/', views.CreateLSUsmlFileView.as_view()),
    path('lsu-social-media-links/<int:pk>/', views.LSUsmlFileDetailView.as_view()),
    path('lsu-social-media-links/<int:pk>/edit/', views.CreateLSUsmlFileView.as_view()),
    path('lsu-social-media-links/<int:pk>/delete/', views.CreateLSUsmlFileView.as_view()),
]