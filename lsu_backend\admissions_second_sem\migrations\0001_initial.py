# Generated by Django 5.0.2 on 2025-01-21 05:27

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccountingDoneEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.Char<PERSON>ield(max_length=255)),
                ('student_lsu_id_number', models.Char<PERSON>ield(max_length=255)),
                ('firstname', models.Char<PERSON>ield(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.Char<PERSON>ield(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='AdmissionFormCompleteEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('student_lsu_id_number', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='AdmissionsDoneEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='AdvisingDoneEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='EnrolleeDataFirstSemModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('extension_or_suffix_name', models.CharField(max_length=255)),
                ('birth_sex', models.CharField(max_length=255)),
                ('birth_date', models.CharField(max_length=255)),
                ('citizenship', models.CharField(max_length=255)),
                ('college', models.CharField(max_length=255)),
                ('program', models.CharField(max_length=255)),
                ('contact_primary_number', models.CharField(max_length=255)),
                ('contact_alternate_number', models.CharField(max_length=255)),
                ('contact_personal_email_address', models.CharField(max_length=255)),
                ('contact_lsu_email_address', models.CharField(max_length=255)),
                ('last_term_enrolled', models.CharField(max_length=255)),
                ('last_academic_year_enrolled', models.CharField(max_length=255)),
                ('alien_status_information_citizenship', models.CharField(max_length=255)),
                ('alien_status_information_visa_status', models.CharField(max_length=255)),
                ('alien_status_information_last_day_of_authorized_stay', models.CharField(max_length=255)),
                ('alien_status_information_agent_name', models.CharField(max_length=255)),
                ('alien_status_information_passport_number', models.CharField(max_length=255)),
                ('alien_status_information_passport_place_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_of_expiry', models.CharField(max_length=255)),
                ('media_release_consent', models.CharField(max_length=255)),
                ('has_health_condition', models.CharField(max_length=255)),
                ('hereby_certification', models.CharField(max_length=255)),
                ('evaluation_submitted', models.CharField(max_length=255)),
                ('receipt_image_url', models.CharField(max_length=255)),
                ('receipt_submitted', models.CharField(max_length=255)),
                ('receipt_confirm', models.CharField(max_length=255)),
                ('shiftee_placement_details', models.CharField(max_length=255)),
                ('shiftee_placement_department', models.CharField(max_length=255)),
                ('shiftee_placement_recommendation_status', models.CharField(max_length=255)),
                ('shiftee_placement_accepting_college', models.CharField(max_length=255)),
                ('shiftee_placement_approval_status', models.CharField(max_length=255)),
                ('enrollment_tracking_status_admissions_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_advising_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_accounting_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_validation_is_complete', models.CharField(max_length=255)),
                ('returnee_student', django_jsonform.models.fields.JSONField(null=True)),
                ('shiftee_student_is_student_shiftee', models.CharField(max_length=255)),
                ('shiftee_student_college', models.CharField(max_length=255)),
                ('shiftee_student_program', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='EnrolleeDataFirstSemNewStudentModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('temporary_lsu_id_number', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('former_lsu_student', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('extension_or_suffix_name', models.CharField(max_length=255)),
                ('birth_sex', models.CharField(max_length=255)),
                ('birth_date', models.CharField(max_length=255)),
                ('citizenship', models.CharField(max_length=255)),
                ('college', models.CharField(max_length=255)),
                ('program', models.CharField(max_length=255)),
                ('contact_primary_number', models.CharField(max_length=255)),
                ('contact_alternate_number', models.CharField(max_length=255)),
                ('contact_personal_email_address', models.CharField(max_length=255)),
                ('contact_lsu_email_address', models.CharField(max_length=255)),
                ('senior_highschool_track', models.CharField(max_length=255)),
                ('senior_highschool_strand', models.CharField(max_length=255)),
                ('last_school_attended', models.CharField(max_length=255)),
                ('shs_year_graduated', models.CharField(max_length=255)),
                ('alien_status_information_citizenship', models.CharField(max_length=255)),
                ('alien_status_information_visa_status', models.CharField(max_length=255)),
                ('alien_status_information_last_day_of_authorized_stay', models.CharField(max_length=255)),
                ('alien_status_information_agent_name', models.CharField(max_length=255)),
                ('alien_status_information_passport_number', models.CharField(max_length=255)),
                ('alien_status_information_passport_place_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_of_expiry', models.CharField(max_length=255)),
                ('media_release_consent', models.CharField(max_length=255)),
                ('has_health_condition', models.CharField(max_length=255)),
                ('hereby_certification', models.CharField(max_length=255)),
                ('enrollment_tracking_status_admissions_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_advising_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_accounting_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_validation_is_complete', models.CharField(max_length=255)),
                ('evaluation_submitted', models.CharField(max_length=255)),
                ('receipt_image_url', models.CharField(max_length=255)),
                ('receipt_submitted', models.CharField(max_length=255)),
                ('receipt_confirm', models.CharField(max_length=255)),
                ('new_student_placement_details', models.CharField(max_length=255)),
                ('new_student_placement_department', models.CharField(max_length=255)),
                ('new_student_placement_test_administration_status', models.CharField(max_length=255)),
                ('new_student_placement_counselor_student_encounter_status', models.CharField(max_length=255)),
                ('credential_student_portal_password', models.CharField(max_length=255)),
                ('credential_student_lsu_email_username', models.CharField(max_length=255)),
                ('credential_student_lsu_email_password', models.CharField(max_length=255)),
                ('credential_student_portal_username', models.CharField(max_length=255)),
                ('credential_student_canvas_password', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='EnrolleeDataFirstSemSecondDegreeHolderStudentModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('temporary_lsu_id_number', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('former_lsu_student', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('extension_or_suffix_name', models.CharField(max_length=255)),
                ('birth_sex', models.CharField(max_length=255)),
                ('birth_date', models.CharField(max_length=255)),
                ('citizenship', models.CharField(max_length=255)),
                ('college', models.CharField(max_length=255)),
                ('program', models.CharField(max_length=255)),
                ('contact_primary_number', models.CharField(max_length=255)),
                ('contact_alternate_number', models.CharField(max_length=255)),
                ('contact_personal_email_address', models.CharField(max_length=255)),
                ('contact_lsu_email_address', models.CharField(max_length=255)),
                ('last_school_graduated', models.CharField(max_length=255)),
                ('last_term_graduated', models.CharField(max_length=255)),
                ('last_academic_year_graduated', models.CharField(max_length=255)),
                ('alien_status_information_citizenship', models.CharField(max_length=255)),
                ('alien_status_information_visa_status', models.CharField(max_length=255)),
                ('alien_status_information_last_day_of_authorized_stay', models.CharField(max_length=255)),
                ('alien_status_information_agent_name', models.CharField(max_length=255)),
                ('alien_status_information_passport_number', models.CharField(max_length=255)),
                ('alien_status_information_passport_place_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_of_expiry', models.CharField(max_length=255)),
                ('media_release_consent', models.CharField(max_length=255)),
                ('has_health_condition', models.CharField(max_length=255)),
                ('hereby_certification', models.CharField(max_length=255)),
                ('enrollment_tracking_status_admissions_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_advising_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_accounting_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_validation_is_complete', models.CharField(max_length=255)),
                ('evaluation_submitted', models.CharField(max_length=255)),
                ('receipt_image_url', models.CharField(max_length=255)),
                ('receipt_submitted', models.CharField(max_length=255)),
                ('receipt_confirm', models.CharField(max_length=255)),
                ('second_degree_holder_student_college', models.CharField(max_length=255)),
                ('second_degree_holder_student_program', models.CharField(max_length=255)),
                ('credential_student_portal_password', models.CharField(max_length=255)),
                ('credential_student_lsu_email_username', models.CharField(max_length=255)),
                ('credential_student_lsu_email_password', models.CharField(max_length=255)),
                ('credential_student_portal_username', models.CharField(max_length=255)),
                ('credential_student_canvas_password', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='EnrolleeDataFirstSemTransfereeStudentModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('temporary_lsu_id_number', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('former_lsu_student', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('extension_or_suffix_name', models.CharField(max_length=255)),
                ('birth_sex', models.CharField(max_length=255)),
                ('birth_date', models.CharField(max_length=255)),
                ('citizenship', models.CharField(max_length=255)),
                ('college', models.CharField(max_length=255)),
                ('program', models.CharField(max_length=255)),
                ('contact_primary_number', models.CharField(max_length=255)),
                ('contact_alternate_number', models.CharField(max_length=255)),
                ('contact_personal_email_address', models.CharField(max_length=255)),
                ('contact_lsu_email_address', models.CharField(max_length=255)),
                ('last_school_attended', models.CharField(max_length=255)),
                ('last_term_enrolled', models.CharField(max_length=255)),
                ('last_academic_year_enrolled', models.CharField(max_length=255)),
                ('alien_status_information_citizenship', models.CharField(max_length=255)),
                ('alien_status_information_visa_status', models.CharField(max_length=255)),
                ('alien_status_information_last_day_of_authorized_stay', models.CharField(max_length=255)),
                ('alien_status_information_agent_name', models.CharField(max_length=255)),
                ('alien_status_information_passport_number', models.CharField(max_length=255)),
                ('alien_status_information_passport_place_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_passport_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_acricard_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_crts_date_of_expiry', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_issued', models.CharField(max_length=255)),
                ('alien_status_information_ssp_date_of_expiry', models.CharField(max_length=255)),
                ('media_release_consent', models.CharField(max_length=255)),
                ('has_health_condition', models.CharField(max_length=255)),
                ('hereby_certification', models.CharField(max_length=255)),
                ('enrollment_tracking_status_admissions_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_advising_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_accounting_is_complete', models.CharField(max_length=255)),
                ('enrollment_tracking_status_validation_is_complete', models.CharField(max_length=255)),
                ('evaluation_submitted', models.CharField(max_length=255)),
                ('receipt_image_url', models.CharField(max_length=255)),
                ('receipt_submitted', models.CharField(max_length=255)),
                ('receipt_confirm', models.CharField(max_length=255)),
                ('transferee_student_college', models.CharField(max_length=255)),
                ('transferee_student_program', models.CharField(max_length=255)),
                ('transferee_student_placement_details', models.CharField(max_length=255)),
                ('transferee_student_placement_department', models.CharField(max_length=255)),
                ('transferee_student_placement_test_administration_status', models.CharField(max_length=255)),
                ('transferee_student_placement_counselor_student_encounter_status', models.CharField(max_length=255)),
                ('transferee_student_placement_accepting_college', models.CharField(max_length=255)),
                ('transferee_student_placement_approval_status', models.CharField(max_length=255)),
                ('credential_student_portal_password', models.CharField(max_length=255)),
                ('credential_student_lsu_email_username', models.CharField(max_length=255)),
                ('credential_student_lsu_email_password', models.CharField(max_length=255)),
                ('credential_student_portal_username', models.CharField(max_length=255)),
                ('credential_student_canvas_password', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='EvaluationDoneEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='EvaluationFormDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('middlename', models.CharField(max_length=255)),
                ('lastname', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('lsu_email_address', models.CharField(max_length=255)),
                ('personal_email_address', models.CharField(max_length=255)),
                ('evaluation_form', django_jsonform.models.fields.JSONField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='NewTrackingIDEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='ReceiptDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.FileField(upload_to='files/admissions/files/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='StudentProfileModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('primary_info_tracking_id', models.CharField(max_length=255)),
                ('primary_info_student_lsu_id_number', models.CharField(max_length=255)),
                ('primary_info_title', models.CharField(max_length=255)),
                ('primary_info_lastname', models.CharField(max_length=255)),
                ('primary_info_firstname', models.CharField(max_length=255)),
                ('primary_info_middlename', models.CharField(max_length=255)),
                ('primary_info_extension_or_suffix_name', models.CharField(max_length=255)),
                ('primary_info_birth_sex', models.CharField(max_length=255)),
                ('primary_info_birth_date', models.CharField(max_length=255)),
                ('primary_info_birth_order', models.CharField(max_length=255)),
                ('primary_info_birth_place', models.CharField(max_length=255)),
                ('primary_info_religion', models.CharField(max_length=255)),
                ('primary_info_citizenship', models.CharField(max_length=255)),
                ('primary_info_civil_status', models.CharField(max_length=255)),
                ('primary_info_nationality', models.CharField(max_length=255)),
                ('primary_info_ethnicity', models.CharField(max_length=255)),
                ('primary_info_college', models.CharField(max_length=255)),
                ('primary_info_program', models.CharField(max_length=255)),
                ('primary_info_contact_primary_number', models.CharField(max_length=255)),
                ('primary_info_contact_alternate_number', models.CharField(max_length=255)),
                ('primary_info_contact_personal_email_address', models.CharField(max_length=255)),
                ('primary_info_contact_lsu_email_address', models.CharField(max_length=255)),
                ('how_you_learn_about_lsu', django_jsonform.models.fields.JSONField(null=True)),
                ('reasons_for_choosing_lsu', django_jsonform.models.fields.JSONField(null=True)),
                ('household_capacity_and_access_to_distance_learning', django_jsonform.models.fields.JSONField(null=True)),
                ('stud_tribal_or_indigenous_community_option', models.CharField(max_length=255)),
                ('stud_tribal_or_indigenous_community_name', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_street_or_purok', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_barangay_or_village', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_city_or_municipality', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_zipcode', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_province_or_state', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_region', models.CharField(max_length=255)),
                ('stud_cnt_info_perm_lvng_h_adr_ctgry_country', models.CharField(max_length=255)),
                ('stud_cnt_info_the_same_address_question', models.CharField(max_length=255)),
                ('stud_cnt_info_the_same_address_answer', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_street_or_purok', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_barangay_or_village', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_city_or_municipality', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_zipcode', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_province_or_state', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_region', models.CharField(max_length=255)),
                ('stud_cnt_info_c_or_p_lvng_h_adr_ctgry_country', models.CharField(max_length=255)),
                ('f_personal_info_vital_life_status', models.CharField(max_length=255)),
                ('f_personal_info_lastname', models.CharField(max_length=255)),
                ('f_personal_info_firstname', models.CharField(max_length=255)),
                ('f_personal_info_middlename', models.CharField(max_length=255)),
                ('f_personal_info_birth_date', models.CharField(max_length=255)),
                ('f_personal_info_age', models.CharField(max_length=255)),
                ('f_personal_info_civil_status', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_street_or_purok', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_barangay_or_village', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_city_or_municipality', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_zipcode', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_province_or_state', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_region', models.CharField(max_length=255)),
                ('f_cont_info_perm_lvng_h_add_ctgry_country', models.CharField(max_length=255)),
                ('f_cont_info_the_same_adr_question', models.CharField(max_length=255)),
                ('f_cont_info_the_same_adr_answer', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_street_or_purok', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_barangay_or_village', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_city_or_municipality', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_zipcode', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_province_or_state', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_region', models.CharField(max_length=255)),
                ('f_cont_info_curnt_or_pr_lvng_h_adr_ctgy_country', models.CharField(max_length=255)),
                ('f_contact_info_contact_number', models.CharField(max_length=255)),
                ('f_contact_info_contact_email_address', models.CharField(max_length=255)),
                ('f_emp_info_highest_education_completed', models.CharField(max_length=255)),
                ('f_emp_info_occupation', models.CharField(max_length=255)),
                ('f_emp_info_employment_status', models.CharField(max_length=255)),
                ('f_emp_info_gross_monthly_income', models.CharField(max_length=255)),
                ('f_emp_info_employer_or_company', models.CharField(max_length=255)),
                ('m_personal_info_vital_life_status', models.CharField(max_length=255)),
                ('m_personal_info_lastname', models.CharField(max_length=255)),
                ('m_personal_info_firstname', models.CharField(max_length=255)),
                ('m_personal_info_middlename', models.CharField(max_length=255)),
                ('m_personal_info_birth_date', models.CharField(max_length=255)),
                ('m_personal_info_age', models.CharField(max_length=255)),
                ('m_personal_info_civil_status', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_street_or_purok', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_barangay_or_village', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_city_or_municipality', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_zipcode', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_province_or_state', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_region', models.CharField(max_length=255)),
                ('m_contact_info_perm_lvng_home_add_ctgy_country', models.CharField(max_length=255)),
                ('m_contact_info_the_same_address_question', models.CharField(max_length=255)),
                ('m_contact_info_the_same_address_no', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_street_or_purok', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_barangay_or_village', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_city_or_municipality', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_zipcode', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_province_or_state', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_region', models.CharField(max_length=255)),
                ('m_cont_info_crnt_or_prst_lvng_h_adr_ctgry_country', models.CharField(max_length=255)),
                ('m_cont_info_contact_number', models.CharField(max_length=255)),
                ('m_cont_info_contact_email_address', models.CharField(max_length=255)),
                ('m_emp_info_highest_education_completed', models.CharField(max_length=255)),
                ('m_emp_info_occupation', models.CharField(max_length=255)),
                ('m_emp_info_employment_status', models.CharField(max_length=255)),
                ('m_emp_info_gross_monthly_income', models.CharField(max_length=255)),
                ('m_emp_info_employer_or_company', models.CharField(max_length=255)),
                ('lg_prsnl_info_relation_to_student', models.CharField(max_length=255)),
                ('lg_prsnl_info_lastname', models.CharField(max_length=255)),
                ('lg_prsnl_info_firstname', models.CharField(max_length=255)),
                ('lg_prsnl_info_middlename', models.CharField(max_length=255)),
                ('lg_prsnl_info_birth_date', models.CharField(max_length=255)),
                ('lg_prsnl_info_age', models.CharField(max_length=255)),
                ('lg_prsnl_info_civil_status', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_street_or_purok', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_barangay_or_village', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_city_or_municipality', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_zipcode', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_province_or_state', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_region', models.CharField(max_length=255)),
                ('lg_cont_info_perm_lvng_h_adr_ctgy_country', models.CharField(max_length=255)),
                ('lg_cont_info_the_same_address_question', models.CharField(max_length=255)),
                ('lg_cont_info_the_same_address_answer', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_street_or_purok', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_barangay_or_village', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_city_or_municipality', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_zipcode', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_province_or_state', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_region', models.CharField(max_length=255)),
                ('lg_cont_info_crnt_or_prst_lvng_h_adr_ctry_country', models.CharField(max_length=255)),
                ('lg_cont_info_contact_number', models.CharField(max_length=255)),
                ('lg_cont_info_contact_email_address', models.CharField(max_length=255)),
                ('lg_emp_info_highest_education_completed', models.CharField(max_length=255)),
                ('lg_emp_info_occupation', models.CharField(max_length=255)),
                ('lg_emp_info_employment_status', models.CharField(max_length=255)),
                ('lg_emp_info_gross_monthly_income', models.CharField(max_length=255)),
                ('lg_emp_info_employer_or_company', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_title', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_lastname', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_firstname', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_middlename', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_extension_or_suffix_name', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_p_info_relation_to_student', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_street_or_purok', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_barangay_or_village', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_city_or_municipality', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_zipcode', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_province_or_state', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_region', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_adr_country', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_cnt_primary_number', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_cnt_alternate_number', models.CharField(max_length=255)),
                ('stud_emrgcy_cont_info_cnt_email_address', models.CharField(max_length=255)),
                ('stud_choice_trck_prog_one_course_program', models.CharField(max_length=255)),
                ('stud_choice_trck_prog_two_course_program', models.CharField(max_length=255)),
                ('stud_choice_trck_prog_three_course_program', models.CharField(max_length=255)),
                ('student_educational_info', django_jsonform.models.fields.JSONField(null=True)),
                ('student_education_info_num_learner_reference_number', models.CharField(max_length=255)),
                ('student_education_info_num_ched_award_number', models.CharField(max_length=255)),
                ('student_education_info_num_dswd_household_number', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='ValidationEmailNotificationDataModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tracking_id', models.CharField(max_length=255)),
                ('student_lsu_id_number', models.CharField(max_length=255)),
                ('firstname', models.CharField(max_length=255)),
                ('email', models.CharField(max_length=255)),
                ('lsu_email', models.CharField(max_length=255)),
                ('credential_student_portal_password', models.CharField(max_length=255)),
                ('credential_student_lsu_email_username', models.CharField(max_length=255)),
                ('credential_student_lsu_email_password', models.CharField(max_length=255)),
                ('credential_student_portal_username', models.CharField(max_length=255)),
                ('credential_student_canvas_password', models.CharField(max_length=255)),
            ],
        ),
    ]
