from rest_framework.response import Response
from rest_framework.views import APIView
from django.core import mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework import viewsets
from .forms import AppointmentForm, TrackingForm
from .models import Appointment, Tracking, Image
from .serializers import AppointmentSerializer, AppointmentDetailSerializer, TrackingSerializer, TrackingDetailSerializer, ImageSerializer
from django.conf import settings

class SubmitAppointmentToGmail(APIView):
  def post(self, request):
    form = AppointmentForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "lastname"  : form['lastname'].value(),
      "banner_image"  : form['banner_image'].value(),
      "referencecode" : form['referencecode'].value(),
    }
    if form.is_valid():
      subject = 'Appointment Confirmation'
      html_message = render_to_string('appointment.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('email')
      mail.send_mail(subject, plain_message, settings.EMAIL_HOST_USER, [recipient], html_message=html_message, fail_silently=False)
      return Response({'status':'sent'})

class ListAppointmentsView(APIView):
  def get(self, request, format=None):
    appointments = Appointment.objects.all()
    serializer = AppointmentSerializer(appointments, many=True)
    return Response(serializer.data)

class CreateAppointmentView(APIView):
  def post(self, request):
    form = AppointmentForm(request.data)

    if form.is_valid():
      appointment = form.save(commit=False)
      appointment.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    appointment = Appointment.objects.get(pk=pk)
    form = AppointmentForm(request.data, instance=appointment)
    form.save()
    return Response({'status': 'updated'})
    
  def delete(self, request, pk):
    appointment = Appointment.objects.get(pk=pk)
    appointment.delete()
    return Response({'status': 'deleted'})    

class AppointmentsDetailView(APIView):
  def get(self, request, pk, format=None):
    appointment = Appointment.objects.get(pk=pk)
    serializer = AppointmentDetailSerializer(appointment)

    return Response(serializer.data)

class ListTrackingsView(APIView):
  def get(self, request, format=None):
    trackings = Tracking.objects.all()
    serializer = TrackingSerializer(trackings, many=True)

    return Response(serializer.data)

class CreateTrackingView(APIView):
  def post(self, request):
    form = TrackingForm(request.data)

    if form.is_valid():
      tracking = form.save(commit=False)
      tracking.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    tracking = Tracking.objects.get(pk=pk)
    form = TrackingForm(request.data, instance=tracking)
    form.save()

    return Response({'status': 'updated'})
    
  def delete(self, request, pk):
    tracking = Tracking.objects.get(pk=pk)
    tracking.delete()

    return Response({'status': 'deleted'})

class TrackingsDetailView(APIView):
  def get(self, request, pk, format=None):
    tracking = Tracking.objects.get(pk=pk)
    serializer = TrackingDetailSerializer(tracking)

    return Response(serializer.data)

class ImageViewSet(viewsets.ModelViewSet):
  queryset = Image.objects.all()
  serializer_class = ImageSerializer