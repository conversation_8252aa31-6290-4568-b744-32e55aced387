# Generated by Django 5.0.2 on 2025-05-14 09:39

import django_jsonform.models.fields
import storages.backends.s3
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileUploadModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(storage=storages.backends.s3.S3Storage(), upload_to='registrar/appointment/uploads/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='HEURegistrarAppointmentModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('firstname', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('middlename', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('lastname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('birthdate', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('mother_maiden_name', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('contact_number', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('email', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('alumni', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('college', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('course', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('year_graduated_last_attended', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('type_document_requests', django_jsonform.models.fields.JSONField(null=True)),
                ('details', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('valid_id_front', django_jsonform.models.fields.JSONField(null=True)),
                ('valid_id_back', django_jsonform.models.fields.JSONField(null=True)),
                ('credential_evaluation_requests', django_jsonform.models.fields.JSONField(null=True)),
                ('tracking_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('data_privacy', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('logs', django_jsonform.models.fields.JSONField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
