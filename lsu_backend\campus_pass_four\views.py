from rest_framework.response import Response
from rest_framework.views import APIView
from .forms import CampusPassEntryFormVersionOneForm, GmailNotificationForm
from .models import CampusPassEntryFormVersionThreeModel
from .serializers import CampusPassEntryFormVersionOneSerializer
from django.template.loader import render_to_string
from django.core import mail
from django.utils.html import strip_tags
# Create your views here.

class SubmitAppointmentToGmailPending(APIView):
  def post(self, request):
    form = GmailNotificationForm(request.data)
    context = {
      "incharge_firstname" : form['incharge_firstname'].value(),
      "incharge_contact_email" : form['incharge_contact_email'].value(),
      "schedule" : form['schedule'].value(),
      "approval_status" : form['approval_status'].value(),
      "remarks" : form['remarks'].value(),
      "tracking_id" : form['tracking_id'].value(),
      "purpose" : form['purpose'].value(),
    }
    if form.is_valid():
      subject = 'Confirm Receipt'
      html_message = render_to_string('pending.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('incharge_contact_email')
      recipient_list = [recipient, '<EMAIL>', '<EMAIL>']
      email_from = 'LSU Campus Pass'
      mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
      return Response({'status':'sent'}) 
    
class SubmitAppointmentToGmailDeclined(APIView):
  def post(self, request):
    form = GmailNotificationForm(request.data)
    context = {
      "incharge_firstname" : form['incharge_firstname'].value(),
      "incharge_contact_email" : form['incharge_contact_email'].value(),
      "schedule" : form['schedule'].value(),
      "approval_status" : form['approval_status'].value(),
      "remarks" : form['remarks'].value(),
      "tracking_id" : form['tracking_id'].value(),
      "purpose" : form['purpose'].value()
    }
    if form.is_valid():
      subject = 'Declined'
      html_message = render_to_string('declined.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('incharge_contact_email')
      recipient_list = [recipient, '<EMAIL>', '<EMAIL>']
      email_from = 'LSU Campus Pass'
      mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
      return Response({'status':'sent'}) 
    
class SubmitAppointmentToGmailApproved(APIView):
  def post(self, request):
    form = GmailNotificationForm(request.data)
    context = {
      "incharge_firstname" : form['incharge_firstname'].value(),
      "incharge_contact_email" : form['incharge_contact_email'].value(),
      "schedule" : form['schedule'].value(),
      "approval_status" : form['approval_status'].value(),
      "remarks" : form['remarks'].value(),
      "tracking_id" : form['tracking_id'].value(),
      "purpose" : form['purpose'].value()
    }
    if form.is_valid():
      subject = 'Approved'
      html_message = render_to_string('approved.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('incharge_contact_email')
      recipient_list = [recipient, '<EMAIL>', '<EMAIL>']
      email_from = 'LSU Campus Pass'
      mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
      return Response({'status':'sent'}) 
    
class SubmitAppointmentToGmailForRevision(APIView):
  def post(self, request):
    form = GmailNotificationForm(request.data)
    context = {
      "incharge_firstname" : form['incharge_firstname'].value(),
      "incharge_contact_email" : form['incharge_contact_email'].value(),
      "schedule" : form['schedule'].value(),
      "approval_status" : form['approval_status'].value(),
      "remarks" : form['remarks'].value(),
      "tracking_id" : form['tracking_id'].value(),
      "purpose" : form['purpose'].value()
    }
    if form.is_valid():
      subject = 'In Progress'
      html_message = render_to_string('for-revision.html', context)
      plain_message = strip_tags(html_message)
      recipient = form.cleaned_data.get('incharge_contact_email')
      recipient_list = [recipient, '<EMAIL>', '<EMAIL>']
      email_from = 'LSU Campus Pass'
      mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
      return Response({'status':'sent'}) 

class ListView(APIView):
  def get(self, request, format=None):
    obj = CampusPassEntryFormVersionThreeModel.objects.all()
    serializer = CampusPassEntryFormVersionOneSerializer(obj, many=True)

    return Response(serializer.data)

class CreateView(APIView):
  def post(self, request):
    form = CampusPassEntryFormVersionOneForm(request.data)

    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()

      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
    
  def put(self, request, pk):
    obj = CampusPassEntryFormVersionThreeModel.objects.get(pk=pk)
    form = CampusPassEntryFormVersionOneForm(request.data, instance=obj)
    form.save()

    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = CampusPassEntryFormVersionThreeModel.objects.get(pk=pk)
    obj.delete()

    return Response({'status': 'deleted'})
  
class DetailView(APIView):
  def get(self, request, pk, format=None):
    obj = CampusPassEntryFormVersionThreeModel.objects.get(pk=pk)
    serializer = CampusPassEntryFormVersionOneSerializer(obj)

    return Response(serializer.data)