from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics, parsers, status
from .forms import HEURegistrarAppointmentForm, RequestPaymentFeeForm
from .models import HEURegistrarAppointmentModel, RequestPaymentFeeModel
from .serializers import HEURegistrarAppointmentSerializer, FileUploadSerializer, RequestPaymentFeeSerializer
from django.template.loader import render_to_string
from django.core import mail
from django.utils.html import strip_tags
import json

def get_registrar_email_by_college(college):
    """
    Returns the appropriate registrar email based on the college/department.
    """
    college_email_mapping = {
        "Arts and Sciences, Engineering, Architecture, Computer Studies": "<EMAIL>",
        "Business-Related Courses, BSTM": "<EMAIL>",
        "Nursing, and Graduate Studies": "<EMAIL>",
        "Education Courses, BSHM": "<EMAIL>",
        "Criminology": "<EMAIL>"
    }

    # Return the specific registrar email or default to dev email if college not found
    return college_email_mapping.get(college)

class RegistrarAppointmentListView(APIView):
    def get(self, request, format=None):
        obj = HEURegistrarAppointmentModel.objects.all()
        serializer = HEURegistrarAppointmentSerializer(obj, many=True)

        return Response(serializer.data)
 
class RegistrarAppointmentCreateView(APIView):
    def post(self, request):
        form = HEURegistrarAppointmentForm(request.data)

        if form.is_valid():
            form_input = form.save(commit=False)
            form_input.save()

            return Response({'status':'created'})
        else:
            return Response({'status':'errors', 'errors': form.errors})
  
    def put(self, request, pk):
        form_input = HEURegistrarAppointmentModel.objects.get(pk=pk)
        form = HEURegistrarAppointmentForm(request.data, instance=form_input)
        form.save()

        return Response({'status': 'updated'})
    
    def delete(self, request, pk):
        form_input = HEURegistrarAppointmentModel.objects.get(pk=pk)
        form_input.delete()

        return Response({'status': 'deleted'})
    
class RegistrarAppointmentDetailView(APIView):
    def get(self, request, pk, format=None):
        obj = HEURegistrarAppointmentModel.objects.get(pk=pk)
        serializer = HEURegistrarAppointmentSerializer(obj)

        return Response(serializer.data)
    
class RegistrarPaymentFeeListView(APIView):
    def get(self, request, format=None):
        obj = RequestPaymentFeeModel.objects.all()
        serializer = RequestPaymentFeeSerializer(obj, many=True)

        return Response(serializer.data)
 
class RegistrarPaymentFeeCreateView(APIView):
    def post(self, request):
        form = RequestPaymentFeeForm(request.data)

        if form.is_valid():
            form_input = form.save(commit=False)
            form_input.save()

            return Response({'status':'created'})
        else:
            return Response({'status':'errors', 'errors': form.errors})
  
    def put(self, request, pk):
        form_input = RequestPaymentFeeModel.objects.get(pk=pk)
        form = RequestPaymentFeeForm(request.data, instance=form_input)
        form.save()

        return Response({'status': 'updated'})
    
    def delete(self, request, pk):
        form_input = RequestPaymentFeeModel.objects.get(pk=pk)
        form_input.delete()

        return Response({'status': 'deleted'})
    
class RegistrarPaymentFeeDetailView(APIView):
    def get(self, request, pk, format=None):
        obj = RequestPaymentFeeModel.objects.get(pk=pk)
        serializer = RequestPaymentFeeSerializer(obj)

        return Response(serializer.data)
    
class FileUploadView(generics.GenericAPIView):
    parser_classes = (parsers.MultiPartParser,)
    serializer_class = FileUploadSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            uploaded_files = serializer.save()  # Get the list of instances
            return Response(serializer.to_representation(uploaded_files), status=status.HTTP_201_CREATED) # Serialize the list
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SubmitAppointmentToGmail(APIView):
    def post(self, request):
        form = HEURegistrarAppointmentForm(request.data)
        
        # Format document requests for email
        doc_requests = []
        if 'type_document_requests' in request.data and request.data['type_document_requests']:
            try:
                doc_requests = request.data['type_document_requests']
                if isinstance(doc_requests, str):
                    # Try to parse JSON string if needed
                    import json
                    doc_requests = json.loads(doc_requests)
            except Exception as e:
                print(f"Error parsing document requests: {e}")
                doc_requests = []
        
        # Format file uploads for email
        def format_files(files_data):
            if not files_data:
                return []
            
            try:
                if isinstance(files_data, str):
                    import json
                    files_data = json.loads(files_data)
                
                return [
                    {
                        'name': file.get('name', 'Unnamed file'),
                        'url': file.get('url', '#')
                    }
                    for file in files_data
                ]
            except Exception as e:
                print(f"Error formatting files: {e}")
                return []
        
        # Get file data
        valid_id_front = format_files(request.data.get('valid_id_front', []))
        valid_id_back = format_files(request.data.get('valid_id_back', []))
        credential_files = format_files(request.data.get('credential_evaluation_requests', []))
        
        # Initialize logs with submission timestamp (optional)
        logs = None
        if request.data.get('include_logs', True):  # Default to including logs unless specified
            import datetime
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logs = [
                {
                    "timestamp": current_time,
                    "status_remarks": "Application submitted"
                }
            ]
        
        context = {
            "firstname": form['firstname'].value(),
            "middlename": form['middlename'].value(),
            "lastname": form['lastname'].value(),
            "birthdate": form['birthdate'].value(),
            "mother_maiden_name": form['mother_maiden_name'].value(),
            "contact_number": form['contact_number'].value(),
            "email": form['email'].value(),
            "alumni": form['alumni'].value(),
            "college": form['college'].value(),
            "course": form['course'].value(),
            "year_graduated_last_attended": form['year_graduated_last_attended'].value(),
            "details": form['details'].value(),
            "tracking_id": form['tracking_id'].value(),
            "data_privacy": form['data_privacy'].value(),
            
            # Add the array fields
            "document_requests": doc_requests,
            "valid_id_front": valid_id_front,
            "valid_id_back": valid_id_back,
            "credential_files": credential_files,
            
            # Add file counts for convenience
            "front_id_count": len(valid_id_front),
            "back_id_count": len(valid_id_back),
            "credential_files_count": len(credential_files),
            
            # Add logs (may be None)
            "logs": logs
        }
        
        if form.is_valid():
            # Save form with optional logs
            form_input = form.save(commit=False)
            
            # Only set logs if they exist
            if logs is not None:
                form_input.logs = logs
            
            # Set status if logs exist, otherwise leave as default
            if logs is not None:
                form_input.status = "pending"
                
            form_input.save()
            
            subject = 'Document Request'
            html_message = render_to_string('confirm.html', context)
            plain_message = strip_tags(html_message)
            recipient = form.cleaned_data.get('email')
            # Get the appropriate registrar email based on college
            college = form.cleaned_data.get('college', '')
            registrar_email = get_registrar_email_by_college(college)
            recipient_list = [recipient, registrar_email, '<EMAIL>', '<EMAIL>']
            email_from = 'La Salle University Registrar'
            mail.send_mail(subject, plain_message, email_from, recipient_list, html_message=html_message, fail_silently=False)
            return Response({'status': 'sent'})
        else:
            return Response({'status': 'error', 'errors': form.errors}, status=status.HTTP_400_BAD_REQUEST)

class SendStatusUpdateEmail(APIView):
    def post(self, request):
        # Get the appointment ID from the request
        appointment_id = request.data.get('id')
        
        # Debug logging
        print(f"Received data: {request.data}")
        
        if not appointment_id:
            return Response({
                'status': 'error', 
                'message': 'Appointment ID is required',
                'received_data': request.data
            }, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            # Retrieve the appointment record
            appointment = HEURegistrarAppointmentModel.objects.get(pk=appointment_id)
            
            # Format document requests for email
            doc_requests = []
            if hasattr(appointment, 'type_document_requests') and appointment.type_document_requests:
                try:
                    if isinstance(appointment.type_document_requests, str):
                        import json
                        doc_requests = json.loads(appointment.type_document_requests)
                    else:
                        doc_requests = appointment.type_document_requests
                except Exception as e:
                    print(f"Error parsing document requests: {e}")
            
            # Format file uploads for email
            def format_files(files_data):
                if not files_data:
                    return []
                
                try:
                    if isinstance(files_data, str):
                        import json
                        files_data = json.loads(files_data)
                    
                    return [
                        {
                            'name': file.get('name', 'Unnamed file'),
                            'url': file.get('url', '#')
                        }
                        for file in files_data
                    ]
                except Exception as e:
                    print(f"Error formatting files: {e}")
                    return []
            
            # Get file data - safely check if attributes exist
            valid_id_front = format_files(getattr(appointment, 'valid_id_front', None))
            valid_id_back = format_files(getattr(appointment, 'valid_id_back', None))
            credential_files = format_files(getattr(appointment, 'credential_evaluation_requests', None))
            
            # Prepare context for email template
            context = {
                "firstname": getattr(appointment, 'firstname', ''),
                "middlename": getattr(appointment, 'middlename', ''),
                "lastname": getattr(appointment, 'lastname', ''),
                "birthdate": getattr(appointment, 'birthdate', ''),
                "mother_maiden_name": getattr(appointment, 'mother_maiden_name', ''),
                "contact_number": getattr(appointment, 'contact_number', ''),
                "email": getattr(appointment, 'email', ''),
                "alumni": getattr(appointment, 'alumni', ''),
                "college": getattr(appointment, 'college', ''),
                "course": getattr(appointment, 'course', ''),
                "year_graduated_last_attended": getattr(appointment, 'year_graduated_last_attended', ''),
                "remarks": getattr(appointment, 'remarks', ''),
                "tracking_id": getattr(appointment, 'tracking_id', ''),
                "data_privacy": getattr(appointment, 'data_privacy', ''),
                
                # Add the array fields
                "document_requests": doc_requests,
                "valid_id_front": valid_id_front,
                "valid_id_back": valid_id_back,
                "credential_files": credential_files,
                
                # Add file counts for convenience
                "front_id_count": len(valid_id_front),
                "back_id_count": len(valid_id_back),
                "credential_files_count": len(credential_files),
                
                # Include logs for status updates
                "logs": getattr(appointment, 'logs', []),
                
                # Add latest status update for highlighting if provided
                "latest_status": request.data.get('latest_status', '')
            }
            
            # Send the email
            subject = f'Status Update: Document Request {getattr(appointment, "tracking_id", "")}'
            html_message = render_to_string('confirm.html', context)
            plain_message = strip_tags(html_message)
            recipient = getattr(appointment, 'email', '')
            
            if not recipient:
                return Response({
                    'status': 'error', 
                    'message': 'Recipient email not found'
                }, status=status.HTTP_400_BAD_REQUEST)
                
            # Get the appropriate registrar email based on college
            college = getattr(appointment, 'college', '')
            registrar_email = get_registrar_email_by_college(college)
            recipient_list = [recipient, registrar_email]
            email_from = 'La Salle University Registrar'
            
            mail.send_mail(
                subject, 
                plain_message, 
                email_from, 
                recipient_list, 
                html_message=html_message, 
                fail_silently=False
            )
            
            return Response({'status': 'sent'})
            
        except HEURegistrarAppointmentModel.DoesNotExist:
            return Response(
                {'status': 'error', 'message': f'Appointment with ID {appointment_id} not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return Response(
                {'status': 'error', 'message': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class SendRequestPaymentEmail(APIView):
    def post(self, request):
        try:
            data = request.data

            # Log incoming data for debug
            print("Incoming POST data:", data)

            # Validate required fields
            required_fields = ['payment_id', 'fullname', 'email', 'course', 'date_graduated_last_attended', 'total', 'detail_fees']
            missing_fields = [field for field in required_fields if not data.get(field)]
            if missing_fields:
                return Response({
                    'status': 'error',
                    'message': f"Missing required fields: {', '.join(missing_fields)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create and save the payment record
            payment = RequestPaymentFeeModel.objects.create(
                payment_id=data.get('payment_id'),
                fullname=data.get('fullname'),
                email=data.get('email'),
                course=data.get('course'),
                date_graduated_last_attended=data.get('date_graduated_last_attended'),
                total=data.get('total'),
                detail_fees=data.get('detail_fees'),
            )

            # Prepare email context
            context = {
                'request': {
                    'fullname': payment.fullname,
                    'course': payment.course,
                    'date_graduated_last_attended': payment.date_graduated_last_attended,
                    'detail_fees': payment.detail_fees,
                    'total': payment.total,
                    'created_at': payment.created_at.strftime('%B %d, %Y'),
                }
            }

            # Compose email
            subject = "[LSU Ozamiz] Document Request Payment Details"
            html_content = render_to_string("ccsea.html", context)
            text_content = strip_tags(html_content)

            # Send email
            mail.send_mail(
                subject=subject,
                message=text_content,
                from_email="<EMAIL>",
                recipient_list=[payment.email, "<EMAIL>"],
                html_message=html_content,
                fail_silently=False
            )

            return Response({
                'status': 'sent',
                'message': 'Email sent successfully.',
                'data': {
                    'payment_id': payment.payment_id
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            print(f"[Exception] {e}")
            return Response({
                'status': 'error',
                'message': f'Unexpected error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)