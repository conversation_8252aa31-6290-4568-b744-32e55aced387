from rest_framework.response import Response
from rest_framework.views import APIView
from django.core.mail import send_mail
from django.utils.html import strip_tags
from rest_framework import viewsets
from django.conf import settings
from django.template import Context
from django.template.loader import render_to_string, get_template
from django.core.mail import EmailMessage, EmailMultiAlternatives
from email.mime.image import MIMEImage
from .models import EnrolleeDataFirstSemModel, EnrolleeDataFirstSemNewStudentModel, EnrolleeDataFirstSemTransfereeStudentModel, EnrolleeDataFirstSemSecondDegreeHolderStudentModel, EvaluationFormDataModel, ReceiptDataModel, StudentProfileModel
from .forms import EnrolleeDataFirstSemForm, EnrolleeDataFirstSemNewStudentForm, EnrolleeDataFirstSemTransfereeStudentForm, EnrolleeDataFirstSemSecondDegreeHolderStudentForm, EvaluationFormDataForm, AdmissionsDoneEmailNotificationDataForm, StudentProfileForm, ValidationEmailNotificationDataForm, AdvisingDoneEmailNotificationDataForm, AccountingDoneEmailNotificationDataForm, EvaluationDoneEmailNotificationDataForm, NewTrackingIDEmailNotificationDataForm
from .serializers import EnrolleeDataFirstSemSerializer, EnrolleeDataFirstSemNewStudentSerializer, EnrolleeDataFirstSemTransfereeStudentSerializer, EnrolleeDataFirstSemSecondDegreeHolderStudentSerializer, EvaluationFormDataSerializer, ReceiptDataSerializer, StudentProfileSerializer

class SubmitNewStudentToGmail(APIView):
  def post(self, request):
    form = AdmissionsDoneEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'LSU Admissions Form Completed'
      html_content = render_to_string('newstudent.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Admissions and Scholarships Center',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class SubmitNewTrackingID(APIView):
  def post(self, request):
    form = NewTrackingIDEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'New LSU Tracking ID'
      html_content = render_to_string('newtrackingid.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Admissions and Scholarships Center',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AdmissionsDone(APIView):
  def post(self, request):
    form = AdmissionsDoneEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Admissions Verified'
      html_content = render_to_string('admissionsdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AdvisingDone(APIView):
  def post(self, request):
    form = AdvisingDoneEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Course Advising Completed'
      html_content = render_to_string('advisingdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class AccountingDone(APIView):
  def post(self, request):
    form = AccountingDoneEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'Accounting Confirmation'
      html_content = render_to_string('accountingdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class EvaluationDone(APIView):
  def post(self, request):
    form = EvaluationDoneEmailNotificationDataForm(request.data)
    context = {
      "firstname" : form['firstname'].value(),
      "tracking_id" : form['tracking_id'].value(),
    }
    if form.is_valid():
      subject = 'LSU Enrollment Certificate of Registration'
      html_content = render_to_string('evaluationdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class ValidationDone(APIView):
  def post(self, request):
    form = ValidationEmailNotificationDataForm(request.data)
    context = {
      "tracking_id" : form['tracking_id'].value(),
      "student_lsu_id_number" : form['student_lsu_id_number'].value(),
      "firstname" : form['firstname'].value(),
      "email" : form['email'].value(),
      "credential_student_portal_username" : form['credential_student_portal_username'].value(),
      "credential_student_portal_password" : form['credential_student_portal_password'].value(),
      "credential_student_lsu_email_username" : form['credential_student_lsu_email_username'].value(),
      "credential_student_lsu_email_password" : form['credential_student_lsu_email_password'].value(),
      "credential_student_portal_username" : form['credential_student_portal_username'].value(),
      "credential_student_canvas_password" : form['credential_student_canvas_password'].value(),
    }
    if form.is_valid():
      subject = 'Validation and LSU Account Credentials'
      html_content = render_to_string('validationdone.html', context)
      from_email = "<EMAIL>"
      email = form.cleaned_data.get('email')
      lsu_email = form.cleaned_data.get('lsu_email')
      recipient_list = [email, lsu_email]
      bcc_email = ['<EMAIL>']
      msg = EmailMultiAlternatives(subject, from_email, to=recipient_list, bcc=bcc_email, 
        headers={
          'From': 'LSU Enrollment Tracking System',
          "Reply-To": "<EMAIL>", 
        })
      msg.attach_alternative(html_content, "text/html")
      msg.send(fail_silently=False)
      return Response({'status':'sent'})

class EnrolleeDataFirstSemListView(APIView):
  def get(self, request, format=None):
    obj = EnrolleeDataFirstSemModel.objects.all()
    serializer = EnrolleeDataFirstSemSerializer(obj, many=True)
    return Response(serializer.data)

class EnrolleeDataFirstSemCreateView(APIView):
  def post(self, request):
    form = EnrolleeDataFirstSemForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EnrolleeDataFirstSemModel.objects.get(pk=pk)
    form = EnrolleeDataFirstSemForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EnrolleeDataFirstSemModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EnrolleeDataFirstSemDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EnrolleeDataFirstSemModel.objects.get(pk=pk)
    serializer = EnrolleeDataFirstSemSerializer(obj)
    return Response(serializer.data)

class EnrolleeDataFirstSemNewStudentListView(APIView):
  def get(self, request, format=None):
    obj = EnrolleeDataFirstSemNewStudentModel.objects.all()
    serializer = EnrolleeDataFirstSemNewStudentSerializer(obj, many=True)
    return Response(serializer.data)

class EnrolleeDataFirstSemNewStudentCreateView(APIView):
  def post(self, request):
    form = EnrolleeDataFirstSemNewStudentForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EnrolleeDataFirstSemNewStudentModel.objects.get(pk=pk)
    form = EnrolleeDataFirstSemNewStudentForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EnrolleeDataFirstSemNewStudentModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EnrolleeDataFirstSemNewStudentDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EnrolleeDataFirstSemNewStudentModel.objects.get(pk=pk)
    serializer = EnrolleeDataFirstSemNewStudentSerializer(obj)
    return Response(serializer.data)

class EnrolleeDataFirstSemTransfereeStudentListView(APIView):
  def get(self, request, format=None):
    obj = EnrolleeDataFirstSemTransfereeStudentModel.objects.all()
    serializer = EnrolleeDataFirstSemTransfereeStudentSerializer(obj, many=True)
    return Response(serializer.data)

class EnrolleeDataFirstSemTransfereeStudentCreateView(APIView):
  def post(self, request):
    form = EnrolleeDataFirstSemTransfereeStudentForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EnrolleeDataFirstSemTransfereeStudentModel.objects.get(pk=pk)
    form = EnrolleeDataFirstSemTransfereeStudentForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EnrolleeDataFirstSemTransfereeStudentModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EnrolleeDataFirstSemTransfereeStudentDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EnrolleeDataFirstSemTransfereeStudentModel.objects.get(pk=pk)
    serializer = EnrolleeDataFirstSemTransfereeStudentSerializer(obj)
    return Response(serializer.data)

class EnrolleeDataFirstSemSecondDegreeHolderStudentListView(APIView):
  def get(self, request, format=None):
    obj = EnrolleeDataFirstSemSecondDegreeHolderStudentModel.objects.all()
    serializer = EnrolleeDataFirstSemSecondDegreeHolderStudentSerializer(obj, many=True)
    return Response(serializer.data)

class EnrolleeDataFirstSemSecondDegreeHolderStudentCreateView(APIView):
  def post(self, request):
    form = EnrolleeDataFirstSemSecondDegreeHolderStudentForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EnrolleeDataFirstSemSecondDegreeHolderStudentModel.objects.get(pk=pk)
    form = EnrolleeDataFirstSemSecondDegreeHolderStudentForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EnrolleeDataFirstSemSecondDegreeHolderStudentModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EnrolleeDataFirstSemSecondDegreeHolderStudentDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EnrolleeDataFirstSemSecondDegreeHolderStudentModel.objects.get(pk=pk)
    serializer = EnrolleeDataFirstSemSecondDegreeHolderStudentSerializer(obj)
    return Response(serializer.data)
    
class StudentProfileListView(APIView):
  def get(self, request, format=None):
    obj = StudentProfileModel.objects.all()
    serializer = StudentProfileSerializer(obj, many=True)
    return Response(serializer.data)

class StudentProfileCreateView(APIView):
  def post(self, request):
    form = StudentProfileForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = StudentProfileModel.objects.get(pk=pk)
    form = StudentProfileForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = StudentProfileModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class StudentProfileDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = StudentProfileModel.objects.get(pk=pk)
    serializer = StudentProfileSerializer(obj)
    return Response(serializer.data)

class EvaluationFormDataListView(APIView):
  def get(self, request, format=None):
    obj = EvaluationFormDataModel.objects.all()
    serializer = EvaluationFormDataSerializer(obj, many=True)
    return Response(serializer.data)

class EvaluationFormDataCreateView(APIView):
  def post(self, request):
    form = EvaluationFormDataForm(request.data)
    if form.is_valid():
      obj = form.save(commit=False)
      obj.save()
      return Response({'status':'created'})
    else:
      return Response({'status':'errors', 'errors': form.errors})
  
  def put(self, request, pk):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    form = EvaluationFormDataForm(request.data, instance=obj)
    form.save()
    return Response({'status': 'updated'})
  
  def delete(self, request, pk):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    obj.delete()
    return Response({'status': 'deleted'})

class EvaluationFormDataDetailView(APIView):
  def get(self, request, pk, format=None):
    obj = EvaluationFormDataModel.objects.get(pk=pk)
    serializer = EvaluationFormDataSerializer(obj)
    return Response(serializer.data)

class ReceiptDataViewSet(viewsets.ModelViewSet):
  queryset = ReceiptDataModel.objects.all()
  serializer_class = ReceiptDataSerializer