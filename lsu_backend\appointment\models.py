from django.db import models
from django.template import defaultfilters


class Appointment(models.Model):
 # ITEMS_SCHEMA = {
 #  'type': 'array', # a list which will contain the items
 #  'items': {
 #   'type': 'object', # a list which will contain the items
 #  }
 # }
 service = models.Char<PERSON>ield(max_length=255)
 provider = models.CharField(max_length=255)
 referencecode = models.CharField(max_length=255, null=True)
 date = models.CharField(max_length=255)
 time = models.Char<PERSON>ield(max_length=255)
 firstname = models.Char<PERSON>ield(max_length=255)
 lastname = models.CharField(max_length=255)
 email = models.EmailField()
 contactnumber = models.CharField(max_length=255)
 address = models.Char<PERSON>ield(max_length=255)
 city = models.CharField(max_length=255)
 zipcode = models.Char<PERSON>ield(max_length=255)
 notes = models.TextField(blank=True, null=True)
 banner_image = models.Char<PERSON><PERSON>(max_length=255)
 #banner_image = models.ImageField(upload_to = 'images/')
 successful_request = models.BooleanField(default=True)
 appointment_confirm = models.BooleanField(default=False)
 payment = models.BooleanField(default=False)
 request_delivered = models.Boolean<PERSON>ield(default=False)
 created_by_name = models.CharField(max_length=255)
 created_by_email = models.CharField(max_length=255)
 updated_at = models.CharField(max_length=255)
 created_at = models.DateTimeField(auto_now_add=True)
 
 class Meta:
  ordering = ('-created_at',)
  
 def created_at_formatted(self):
  return defaultfilters.date(self.created_at, 'M d, Y')
 
class Tracking(models.Model):
 referencecode = models.CharField(max_length=255)
 date = models.CharField(max_length=255)
 time = models.CharField(max_length=255)
 description = models.TextField(max_length=255)
 updated_at = models.CharField(max_length=255)
 created_at = models.DateTimeField(auto_now_add=True)

 class Meta:
  ordering = ('-created_at',)
  
 def created_at_formatted(self):
  return defaultfilters.date(self.created_at, 'M d, Y')
 
class Image(models.Model):
  image = models.ImageField(upload_to='files/images/')
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')