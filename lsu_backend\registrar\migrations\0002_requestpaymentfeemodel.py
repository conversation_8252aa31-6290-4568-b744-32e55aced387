# Generated by Django 5.0.2 on 2025-06-09 06:43

import django_jsonform.models.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('registrar', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestPaymentFeeModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fullname', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('course', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('date_graduated_last_attended', models.Char<PERSON>ield(blank=True, default='', max_length=255, null=True)),
                ('detail_fees', django_jsonform.models.fields.JSONField(null=True)),
                ('total', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
