from django.urls import path, include
from . import views 
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
router.register('images', views.ImageViewSet, basename='images')

urlpatterns = [
 path('image/list/', include(router.urls)),
 path('submit-appointment-to-gmail/', views.SubmitAppointmentToGmail.as_view()),
 path('list/', views.ListAppointmentsView.as_view()),
 path('create/', views.CreateAppointmentView.as_view()),
 path('<int:pk>/', views.AppointmentsDetailView.as_view()),
 path('<int:pk>/delete/', views.CreateAppointmentView.as_view()),
 path('<int:pk>/edit/', views.CreateAppointmentView.as_view()),
 path('tracking/create/', views.CreateTrackingView.as_view()),
 path('tracking/list/', views.ListTrackingsView.as_view()),
 path('tracking/<int:pk>/', views.TrackingsDetailView.as_view()),
 path('tracking/<int:pk>/delete/', views.CreateTrackingView.as_view()),
 path('tracking/<int:pk>/edit/', views.CreateTrackingView.as_view()),
 # path('upload/', views.upload_image, name = 'upload_image')
]