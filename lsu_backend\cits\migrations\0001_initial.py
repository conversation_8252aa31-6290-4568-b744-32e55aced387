# Generated by Django 5.0.2 on 2024-04-01 05:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LSUDownloadFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('category', models.Char<PERSON>ield(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='LSUPartnerGMAIL',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('firstname', models.Char<PERSON>ield(max_length=255)),
                ('middlename', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('lastname', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('lsu_email', models.EmailField(max_length=255)),
                ('admin_level_role', models.Char<PERSON>ield(max_length=255)),
                ('lsu_partner_category', models.CharField(max_length=255)),
                ('office', models.CharField(max_length=225)),
                ('office_abbr', models.CharField(max_length=225)),
                ('department', models.CharField(max_length=225)),
                ('department_abbr', models.CharField(max_length=225)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='LSUVolumeFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('category', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
