from rest_framework import serializers

from .models import HEURegistrarAppointmentModel, FileUploadModel, RequestPaymentFeeModel

class HEURegistrarAppointmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = HEURegistrarAppointmentModel
        fields = '__all__'

class FileUploadSerializer(serializers.ModelSerializer):
    class Meta:
        model = FileUploadModel
        fields = '__all__'

class RequestPaymentFeeSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestPaymentFeeModel
        fields = '__all__'