from django.db import models
from django.template import defaultfilters
from django_jsonform.models.fields import J<PERSON><PERSON>ield
from django.contrib.postgres.fields import ArrayField

class EnrolleeDataFirstSemModel(models.Model):
  ALIEN_STATUS_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'citizenship' : {'type' : 'string'},
      'visa_status' : {'type' : 'string'},
      'last_day_of_authorized_stay' : {'type' : 'string'},
      'agent_name' : {'type' : 'string'},
      'passport_number' : {'type' : 'string'},
      'passport_place_issued' : {'type' : 'string'},
      'passport_date_issued' : {'type' : 'string'},
      'passport_date_of_expiry' : {'type' : 'string'},
      'acricard_date_issued' : {'type' : 'string'},
      'acricard_date_of_expiry' : {'type' : 'string'},
      'crts_date_issued' : {'type' : 'string'},
      'crts_date_of_expiry' : {'type' : 'string'},
      'ssp_date_issued' : {'type' : 'string'},
      'ssp_date_of_expiry' : {'type' : 'string'},
    }
  }
  RETURNEE_STUDENT = {
    'type' : 'object',
    'keys' : {
      'is_student_returnee' : {'type' : 'string'},
      'reason_dropping_out_college' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
      'question' : {'type' : 'string'},
      'reasons_for_returning_lsu' : {'type' : 'string'},
    }
  }
  ENROLLMENT_TRACKING_STATUS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'track_name' : {'type' : 'string'},
        'label' : {'type' : 'string'},
        'details' : {'type' : 'string'},
        'date_checked' : {'type' : 'string'},
        'check_by' : {'type' : 'string'},
        'status' : {'type' : 'string'},
        'remarks' : {'type' : 'string'},
      }
    }
  }
  SHIFTEE_STUDENT = {
    'type' : 'object',
    'keys' : {
      'is_student_shiftee' : {'type' : 'string'},
      'college' : {'type' : 'string'},
      'program' : {'type' : 'string'},
    }
  }
  SHIFTEE_PLACEMENT = {
    'type' : 'object',
    'keys' : {
      'details' : {'type' : 'string'},
      'department' : {'type' : 'string'},
      'recommendation_status' : {'type' : 'string'},
      'accepting_college' : {'type' : 'string'},
      'approval_status' : {'type' : 'string'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_term_enrolled = models.CharField(max_length=255)
  last_academic_year_enrolled = models.CharField(max_length=255)
  alien_status_information = JSONField(schema=ALIEN_STATUS_INFORMATION, null=True)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status = JSONField(schema=ENROLLMENT_TRACKING_STATUS, null=True)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  shiftee_placement = JSONField(schema=SHIFTEE_PLACEMENT, null=True)
  returnee_student = JSONField(schema=RETURNEE_STUDENT, null=True)
  shiftee_student = JSONField(schema=SHIFTEE_STUDENT, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id
 
class EnrolleeDataFirstSemNewStudentModel(models.Model):
  ALIEN_STATUS_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'citizenship' : {'type' : 'string'},
      'visa_status' : {'type' : 'string'},
      'last_day_of_authorized_stay' : {'type' : 'string'},
      'agent_name' : {'type' : 'string'},
      'passport_number' : {'type' : 'string'},
      'passport_place_issued' : {'type' : 'string'},
      'passport_date_issued' : {'type' : 'string'},
      'passport_date_of_expiry' : {'type' : 'string'},
      'acricard_date_issued' : {'type' : 'string'},
      'acricard_date_of_expiry' : {'type' : 'string'},
      'crts_date_issued' : {'type' : 'string'},
      'crts_date_of_expiry' : {'type' : 'string'},
      'ssp_date_issued' : {'type' : 'string'},
      'ssp_date_of_expiry' : {'type' : 'string'},
    }
  }
  ENROLLMENT_TRACKING_STATUS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'track_name' : {'type' : 'string'},
        'label' : {'type' : 'string'},
        'details' : {'type' : 'string'},
        'date_checked' : {'type' : 'string'},
        'check_by' : {'type' : 'string'},
        'status' : {'type' : 'string'},
        'remarks' : {'type' : 'string'},
      }
    }
  }
  NEW_STUDENT_PLACEMENT = {
    'type' : 'object',
    'keys' : {
      'details' : {'type' : 'string'},
      'department' : {'type' : 'string'},
      'test_administration_status' : {'type' : 'string'},
      'counselor_student_encounter_status' : {'type' : 'string'}
    }
  }
  CREDENTIALS = {
    'type' : 'object',
    'keys' : {
      'student_portal_username' : {'type' : 'string'},
      'student_portal_password' : {'type' : 'string'},
      'student_lsu_email_username' : {'type' : 'string'},
      'student_lsu_email_password' : {'type' : 'string'},
      'student_canvas_username' : {'type' : 'string'},
      'student_canvas_password' : {'type' : 'string'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  senior_highschool_track = models.CharField(max_length=255)
  senior_highschool_strand = models.CharField(max_length=255)
  last_school_attended = models.CharField(max_length=255)
  shs_year_graduated = models.CharField(max_length=255)
  alien_status_information = JSONField(schema=ALIEN_STATUS_INFORMATION, null=True)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status = JSONField(schema=ENROLLMENT_TRACKING_STATUS, null=True)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  new_student_placement = JSONField(schema=NEW_STUDENT_PLACEMENT, null=True)
  credentials = JSONField(schema=CREDENTIALS, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class EnrolleeDataFirstSemTransfereeStudentModel(models.Model):
  ALIEN_STATUS_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'citizenship' : {'type' : 'string'},
      'visa_status' : {'type' : 'string'},
      'last_day_of_authorized_stay' : {'type' : 'string'},
      'agent_name' : {'type' : 'string'},
      'passport_number' : {'type' : 'string'},
      'passport_place_issued' : {'type' : 'string'},
      'passport_date_issued' : {'type' : 'string'},
      'passport_date_of_expiry' : {'type' : 'string'},
      'acricard_date_issued' : {'type' : 'string'},
      'acricard_date_of_expiry' : {'type' : 'string'},
      'crts_date_issued' : {'type' : 'string'},
      'crts_date_of_expiry' : {'type' : 'string'},
      'ssp_date_issued' : {'type' : 'string'},
      'ssp_date_of_expiry' : {'type' : 'string'},
    }
  }
  ENROLLMENT_TRACKING_STATUS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'track_name' : {'type' : 'string'},
        'label' : {'type' : 'string'},
        'details' : {'type' : 'string'},
        'date_checked' : {'type' : 'string'},
        'check_by' : {'type' : 'string'},
        'status' : {'type' : 'string'},
        'remarks' : {'type' : 'string'},
      }
    }
  }
  TRANSFEREE_STUDENT = {
    'type' : 'object',
    'keys' : {
      'college' : {'type' : 'string'},
      'program' : {'type' : 'string'},
    }
  }
  TRANSFEREE_STUDENT_PLACEMENT = {
    'type' : 'object',
    'keys' : {
      'details' : {'type' : 'string'},
      'department' : {'type' : 'string'},
      'test_administration_status' : {'type' : 'string'},
      'counselor_student_encounter_status' : {'type' : 'string'},
      'accepting_college' : {'type' : 'string'},
      'approval_status' : {'type' : 'string'},
    }
  }
  CREDENTIALS = {
    'type' : 'object',
    'keys' : {
      'student_portal_username' : {'type' : 'string'},
      'student_portal_password' : {'type' : 'string'},
      'student_lsu_email_username' : {'type' : 'string'},
      'student_lsu_email_password' : {'type' : 'string'},
      'student_canvas_username' : {'type' : 'string'},
      'student_canvas_password' : {'type' : 'string'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_school_attended = models.CharField(max_length=255)
  last_term_enrolled = models.CharField(max_length=255)
  last_academic_year_enrolled = models.CharField(max_length=255)
  alien_status_information = JSONField(schema=ALIEN_STATUS_INFORMATION, null=True)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status = JSONField(schema=ENROLLMENT_TRACKING_STATUS, null=True)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  transferee_student = JSONField(schema=TRANSFEREE_STUDENT, null=True)
  transferee_student_placement = JSONField(schema=TRANSFEREE_STUDENT_PLACEMENT, null=True)
  credentials = JSONField(schema=CREDENTIALS, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class EnrolleeDataFirstSemSecondDegreeHolderStudentModel(models.Model):
  ALIEN_STATUS_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'citizenship' : {'type' : 'string'},
      'visa_status' : {'type' : 'string'},
      'last_day_of_authorized_stay' : {'type' : 'string'},
      'agent_name' : {'type' : 'string'},
      'passport_number' : {'type' : 'string'},
      'passport_place_issued' : {'type' : 'string'},
      'passport_date_issued' : {'type' : 'string'},
      'passport_date_of_expiry' : {'type' : 'string'},
      'acricard_date_issued' : {'type' : 'string'},
      'acricard_date_of_expiry' : {'type' : 'string'},
      'crts_date_issued' : {'type' : 'string'},
      'crts_date_of_expiry' : {'type' : 'string'},
      'ssp_date_issued' : {'type' : 'string'},
      'ssp_date_of_expiry' : {'type' : 'string'},
    }
  }
  ENROLLMENT_TRACKING_STATUS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'track_name' : {'type' : 'string'},
        'label' : {'type' : 'string'},
        'details' : {'type' : 'string'},
        'date_checked' : {'type' : 'string'},
        'check_by' : {'type' : 'string'},
        'status' : {'type' : 'string'},
        'remarks' : {'type' : 'string'},
      }
    }
  }
  SECOND_DEGREE_HOLDER_STUDENT = {
    'type' : 'object',
    'keys' : {
      'college' : {'type' : 'string'},
      'program' : {'type' : 'string'},
    }
  }
  CREDENTIALS = {
    'type' : 'object',
    'keys' : {
      'student_portal_username' : {'type' : 'string'},
      'student_portal_password' : {'type' : 'string'},
      'student_lsu_email_username' : {'type' : 'string'},
      'student_lsu_email_password' : {'type' : 'string'},
      'student_canvas_username' : {'type' : 'string'},
      'student_canvas_password' : {'type' : 'string'},
    }
  }
  tracking_id = models.CharField(max_length=255)
  temporary_lsu_id_number = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  former_lsu_student = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  extension_or_suffix_name = models.CharField(max_length=255)
  birth_sex = models.CharField(max_length=255)
  birth_date = models.CharField(max_length=255)
  citizenship = models.CharField(max_length=255)
  college = models.CharField(max_length=255)
  program = models.CharField(max_length=255)
  contact_primary_number = models.CharField(max_length=255)
  contact_alternate_number = models.CharField(max_length=255)
  contact_personal_email_address = models.CharField(max_length=255)
  contact_lsu_email_address = models.CharField(max_length=255)
  last_school_graduated = models.CharField(max_length=255)
  last_term_graduated = models.CharField(max_length=255)
  last_academic_year_graduated = models.CharField(max_length=255)
  alien_status_information = JSONField(schema=ALIEN_STATUS_INFORMATION, null=True)
  media_release_consent = models.CharField(max_length=255)
  has_health_condition = models.CharField(max_length=255)
  hereby_certification = models.CharField(max_length=255)
  enrollment_tracking_status = JSONField(schema=ENROLLMENT_TRACKING_STATUS, null=True)
  evaluation_submitted = models.CharField(max_length=255)
  receipt_image_url = models.CharField(max_length=255)
  receipt_submitted = models.CharField(max_length=255)
  receipt_confirm = models.CharField(max_length=255)
  second_degree_holder_student = JSONField(schema=SECOND_DEGREE_HOLDER_STUDENT, null=True)
  credentials = JSONField(schema=CREDENTIALS, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  def __str__(self):
    return self.tracking_id

class StudentProfileModel(models.Model):
  PRIMARY_INFO = {
    'type' : 'object',
    'keys' : {
      'tracking_id' : {'type' : 'string'},
      'student_lsu_id_number' : {'type' : 'string'},
      'title' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'extension_or_suffix_name' : {'type' : 'string'},
      'birth_sex' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'birth_order' : {'type' : 'string'},
      'birth_place' : {'type' : 'string'},
      'religion' : {'type' : 'string'},
      'citizenship' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
      'nationality' : {'type' : 'string'},
      'ethnicity' : {'type' : 'string'},
      'college' : {'type' : 'string'},
      'program' : {'type' : 'string'},
      'contact_primary_number' : {'type' : 'string'},
      'contact_alternate_number' : {'type' : 'string'},
      'contact_personal_email_address' : {'type' : 'string'},
      'contact_lsu_email_address' : {'type' : 'string'},
    }
  }
  HOW_YOU_LEARN_ABOUT_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  REASONS_FOR_CHOOSING_LSU = {
    'type' : 'object',
    'keys' : {
      'question' : {'type' : 'string'},
      'list_items' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'label' : {'type' : 'string'},
            'value' : {'type' : 'boolean'},
            'description' : {'type' : 'string'},
          }
        }
      },
    }
  }
  HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'question' : {'type' : 'string'},
        'list_items' : {
          'type' : 'array',
          'items' : {
            'type' : 'object',
            'keys' : {
              'label' : {'type' : 'string'},
              'value' : {'type' : 'boolean'},
              'description' : {'type' : 'string'},
            }
          }
        }
      }
    }
  }
  STUDENT_TRIBAL_OR_INDIGENOUS_COMMUNITY = {
    'type' : 'object',
    'keys' : {
      'option' : {'type' : 'string'},
      'name' : {'type' : 'string'},
    }
  }
  STUDENT_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      }
    }
  }
  FATHER_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'vital_life_status' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  FATHER_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  FATHER_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  MOTHER_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'vital_life_status' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  MOTHER_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  MOTHER_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  LEGAL_GUARDIAN_PERSONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'relation_to_student' : {'type' : 'string'},
      'lastname' : {'type' : 'string'},
      'firstname' : {'type' : 'string'},
      'middlename' : {'type' : 'string'},
      'birth_date' : {'type' : 'string'},
      'age' : {'type' : 'string'},
      'civil_status' : {'type' : 'string'},
    }
  }
  LEGAL_GUARDIAN_CONTACT_INFO = {
    'type' : 'object',
    'keys' : {
      'permanent_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'the_same_address' : {
        'type' : 'object',
        'keys' : {
          'question' : {'type' : 'string'},
          'answer' : {'type' : 'string'},
        }
      },
      'current_or_present_living_home_address_category' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  LEGAL_GUARDIAN_EMPLOYMENT_INFO = {
    'type' : 'object',
    'keys' : {
      'highest_education_completed' : {'type' : 'string'},
      'occupation' : {'type' : 'string'},
      'employment_status' : {'type' : 'string'},
      'gross_monthly_income' : {'type' : 'string'},
      'employer_or_company' : {'type' : 'string'},
    }
  }
  STUDENT_EMERGENCY_CONTACT_INFORMATION = {
    'type' : 'object',
    'keys' : {
      'personal_info' : {
        'type' : 'object',
        'keys' : {
          'title' : {'type' : 'string'},
          'lastname' : {'type' : 'string'},
          'firstname' : {'type' : 'string'},
          'middlename' : {'type' : 'string'},
          'extension_or_suffix_name' : {'type' : 'string'},
          'relation_to_student' : {'type' : 'string'},
        }
      },
      'address' : {
        'type' : 'object',
        'keys' : {
          'street_or_purok' : {'type' : 'string'},
          'barangay_or_village' : {'type' : 'string'},
          'city_or_municipality' : {'type' : 'string'},
          'zipcode' : {'type' : 'string'},
          'province_or_state' : {'type' : 'string'},
          'region' : {'type' : 'string'},
          'country' : {'type' : 'string'},
        }
      },
      'contact' : {
        'type' : 'object',
        'keys' : {
          'primary_number' : {'type' : 'string'},
          'alternate_number' : {'type' : 'string'},
          'email_address' : {'type' : 'string'},
        }
      }
    }
  }
  STUDENT_CHOICE = {
    'type' : 'object',
    'keys' : {
      'choice_track_program_one_course_program' : {'type' : 'string'},
      'choice_track_program_two_course_program' : {'type' : 'string'},
      'choice_track_program_three_course_program' : {'type' : 'string'},
    }
  }
  SIBLINGS = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'lastname' : {'type' : 'string'},
        'firstname' : {'type' : 'string'},
        'middlename' : {'type' : 'string'},
        'birth_date' : {'type' : 'string'},
        'age' : {'type' : 'string'},
        'civil_status' : {'type' : 'string'},
        'grade_or_year_level' : {'type' : 'string'},
        'school_or_graduated_from' : {'type' : 'string'},
        'highest_education_completed' : {'type' : 'string'},
        'occupation' : {'type' : 'string'},
        'employer_or_company' : {'type' : 'string'},
      }
    }
  }
  STUDENT_EDUCATIONAL_INFO = {
    'type' : 'object',
    'keys' : {
      'tab' : {
        'type' : 'array',
        'items' : {'type' : 'string'},
      },
      'details' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'education_level' : {'type' : 'string'},
            'name_of_previous_school' : {'type' : 'string'},
            'track_or_program' : {'type' : 'string'},
            'highest_honors_received' : {'type' : 'string'},
            'city_or_municipality_and_province' : {'type' : 'string'},
            'year_graduated_or_attended' : {'type' : 'string'},
          }
        }
      }
    }
  }
  STUDENT_EDUCATION_INFORMATION_NUMBER = {
    'type' : 'object',
    'keys' : {
      'learner_reference_number' : {'type' : 'string'},
      'ched_award_number' : {'type' : 'string'},
      'dswd_household_number' : {'type' : 'string'},
    }
  }
  primary_info = JSONField(schema=PRIMARY_INFO, null=True)
  how_you_learn_about_lsu = JSONField(schema=HOW_YOU_LEARN_ABOUT_LSU, null=True)
  reasons_for_choosing_lsu = JSONField(schema=REASONS_FOR_CHOOSING_LSU, null=True)
  household_capacity_and_access_to_distance_learning = JSONField(schema=HOUSEHOLD_CAPACITY_AND_ACCESS_TO_DISTANCE_LEARNING, null=True)
  student_tribal_or_indigenous_community = JSONField(schema=STUDENT_TRIBAL_OR_INDIGENOUS_COMMUNITY, null=True)
  student_contact_info = JSONField(schema=STUDENT_CONTACT_INFO, null=True)
  father_personal_info = JSONField(schema=FATHER_PERSONAL_INFO, null=True)
  father_contact_info = JSONField(schema=FATHER_CONTACT_INFO, null=True)
  father_employment_info = JSONField(schema=FATHER_EMPLOYMENT_INFO, null=True)
  mother_personal_info = JSONField(schema=MOTHER_PERSONAL_INFO, null=True)
  mother_contact_info = JSONField(schema=MOTHER_CONTACT_INFO, null=True)
  mother_employment_info = JSONField(schema=MOTHER_EMPLOYMENT_INFO, null=True)
  legal_guardian_personal_info = JSONField(schema=LEGAL_GUARDIAN_PERSONAL_INFO, null=True)
  legal_guardian_contact_info = JSONField(schema=LEGAL_GUARDIAN_CONTACT_INFO, null=True)
  legal_guardian_employment_info = JSONField(schema=LEGAL_GUARDIAN_EMPLOYMENT_INFO, null=True)
  student_emergency_contact_information = JSONField(schema=STUDENT_EMERGENCY_CONTACT_INFORMATION, null=True)
  student_choice = JSONField(schema=STUDENT_CHOICE, null=True)
  siblings = JSONField(schema=SIBLINGS, null=True)
  student_educational_info = JSONField(schema=STUDENT_EDUCATIONAL_INFO, null=True)
  student_education_information_number = JSONField(schema=STUDENT_EDUCATION_INFORMATION_NUMBER, null=True)
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')

class EvaluationFormDataModel(models.Model):
  EVALUATION_FORM = {
    'type' : 'object',
    'keys' : {
      'main_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question_heading' : {'type' : 'string'},
            'question_list' : {
              'type' : 'array',
              'items' : {
                'type' : 'object',
                'keys' : {
                  'question' : {'type' : 'string'},
                  'score' : {
                    'type' : 'array',
                    'items' : {
                      'type' : 'object',
                      'keys' : {
                        'number' : {'type' : 'number'},
                        'text' : {'type' : 'string'},
                      }
                    }
                  },
                  'answer' : {'type' : 'string'},
                }
              }
            }
          }
        }
      },
      'sub_question' : {
        'type' : 'array',
        'items' : {
          'type' : 'object',
          'keys' : {
            'question' : {'type' : 'string'},
            'answer' : {'type' : 'string'},
          }
        }
      }
    }
  }
  tracking_id = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  middlename = models.CharField(max_length=255)
  lastname = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  lsu_email_address = models.CharField(max_length=255)
  personal_email_address = models.CharField(max_length=255)
  evaluation_form = JSONField(schema=EVALUATION_FORM, null=True)

  def __str__(self):
    return self.tracking_id

class ReceiptDataModel(models.Model):
  # image = models.ImageField(upload_to='files/admissions/files/')
  image = models.FileField(upload_to='files/admissions/files/')
  created_at = models.DateTimeField(auto_now_add=True)

  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')

class NewTrackingIDEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdmissionFormCompleteEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdmissionsDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AdvisingDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class AccountingDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class EvaluationDoneEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id

class ValidationEmailNotificationDataModel(models.Model):
  tracking_id = models.CharField(max_length=255)
  student_lsu_id_number = models.CharField(max_length=255)
  firstname = models.CharField(max_length=255)
  email = models.CharField(max_length=255)
  lsu_email = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_portal_password = models.CharField(max_length=255)
  credential_student_lsu_email_username = models.CharField(max_length=255)
  credential_student_lsu_email_password = models.CharField(max_length=255)
  credential_student_portal_username = models.CharField(max_length=255)
  credential_student_canvas_password = models.CharField(max_length=255)

  def __str__(self):
    return self.tracking_id