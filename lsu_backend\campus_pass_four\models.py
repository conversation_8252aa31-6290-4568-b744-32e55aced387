from django.db import models
from django.template import defaultfilters
from django_jsonform.models.fields import <PERSON><PERSON>y<PERSON>ield
from django_jsonform.models.fields import JSO<PERSON>ield

class CampusPassEntryFormVersionThreeModel(models.Model):
  ITEMS_SCHEMA_NAME_LIST = {
    'type' : 'array',
    'items' : {
      'type' : 'object',
      'keys' : {
        'firstname' : {
          'type' : 'string'
        },
        'middlename' : {
          'type' : 'string'
        },
        'lastname' : {
          'type' : 'string'
        },
        'email' : {
          'type' : 'string'
        },
        'checkin' : {
          'type' : 'string',
          'default': ''
        },
      }
    }
  }
  ITEMS_SCHEMA_ACCESS_TYPE = {
    'type' : 'array',
    'items' : {
      'type' : 'string'
    }
  }
  incharge_firstname = models.CharField(max_length=255, blank=True, null=True, default='')
  incharge_middlename = models.CharField(max_length=255, blank=True, null=True, default='')
  incharge_lastname = models.Char<PERSON>ield(max_length=255, blank=True, null=True, default='')
  incharge_contact_email = models.CharField(max_length=255, blank=True, null=True, default='')
  incharge_contact_number = models.CharField(max_length=255, blank=True, null=True, default='')
  type_of_access = models.CharField(max_length=255, blank=True, null=True, default='')
  schedule = models.CharField(max_length=255, blank=True, null=True, default='')
  approved_activities_link = models.CharField(max_length=255, blank=True, null=True, default='')
  approved_gso_docs_link = models.CharField(max_length=255, blank=True, null=True, default='')
  attendees = models.CharField(max_length=255, blank=True, null=True, default='')
  name_list = JSONField(schema=ITEMS_SCHEMA_NAME_LIST, null=True)
  approval_status = models.CharField(max_length=255, blank=True, null=True, default='')
  remarks = models.CharField(max_length=255, blank=True, null=True, default='')
  tracking_id = models.CharField(max_length=255, blank=True, null=True, default='')
  purpose = models.CharField(max_length=255, blank=True, null=True, default='')
  checkin = models.CharField(max_length=255, blank=True, null=True, default='')
  created_at = models.DateTimeField(auto_now_add=True)
  
  class Meta: 
    ordering = ('-created_at',)

  def created_at_formatted(self):
    return defaultfilters.date(self.created_at, 'M d, Y')
  
class GmailNotification(models.Model):
  incharge_firstname = models.CharField(max_length=255)
  incharge_contact_email = models.CharField(max_length=255)
  schedule = models.CharField(max_length=255)
  approval_status = models.CharField(max_length=255)
  remarks = models.CharField(max_length=255)
  tracking_id= models.CharField(max_length=255)
  purpose = models.CharField(max_length=255)

  def __str__(self):
    return self.incharge_contact_email