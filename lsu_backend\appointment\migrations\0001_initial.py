# Generated by Django 5.0.2 on 2024-08-28 04:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('provider', models.Char<PERSON>ield(max_length=255)),
                ('referencecode', models.Char<PERSON>ield(max_length=255, null=True)),
                ('date', models.Char<PERSON>ield(max_length=255)),
                ('time', models.Char<PERSON>ield(max_length=255)),
                ('firstname', models.Char<PERSON><PERSON>(max_length=255)),
                ('lastname', models.Char<PERSON>ield(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('contactnumber', models.Char<PERSON>ield(max_length=255)),
                ('address', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('city', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('zipcode', models.Char<PERSON><PERSON>(max_length=255)),
                ('notes', models.TextField(blank=True, null=True)),
                ('banner_image', models.Char<PERSON>ield(max_length=255)),
                ('successful_request', models.BooleanField(default=True)),
                ('appointment_confirm', models.BooleanField(default=False)),
                ('payment', models.BooleanField(default=False)),
                ('request_delivered', models.BooleanField(default=False)),
                ('created_by_name', models.CharField(max_length=255)),
                ('created_by_email', models.CharField(max_length=255)),
                ('updated_at', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='Image',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='files/images/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='Tracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('referencecode', models.CharField(max_length=255)),
                ('date', models.CharField(max_length=255)),
                ('time', models.CharField(max_length=255)),
                ('description', models.TextField(max_length=255)),
                ('updated_at', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
