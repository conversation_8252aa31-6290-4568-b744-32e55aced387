<script setup>
const display = ref("desktop");

onMounted(() =>
  nextTick(() => {
    if (window.innerWidth < 800) {
      display.value = "mobile";
    }
  })
);
</script>

<template>
  <div class="font-montserrat">
    <Header />
    <div v-if="display === 'desktop'">
      <Slider />
    </div>
    <div v-if="display === 'mobile'">
      <SliderMobile />
    </div>
    <div class="lg:relative block z-0">
      <div class="">
        <Shortcuts class="lg:-mt-0.5 bg-white 2xl-shadow" />
      </div>
      <NewsAndUpdates />
    </div>

    <div class="lg:my-5 bg-green-50 py-5 lg:px-5">
      <div
        class="flex items-center lg:w-fit w-11/12 mx-auto lg:gap-x-20 gap-x-3 z-20"
      >
        <a
          target="_blank"
          href="https://www.facebook.com/lsuETC"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/ETC.png"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>
        <a
          target="_blank"
          href="https://www.facebook.com/lsucso"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/CSO.png"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>
        <a
          target="_blank"
          href="https://www.facebook.com/lsuusg"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/USG.png"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>

        <a
          target="_blank"
          href="https://www.facebook.com/lsu.swc"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/Sports.png"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>

        <a
          target="_blank"
          href="https://www.facebook.com/lsuozamizstudentaffairs"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/SAC.jpg"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>

        <a
          target="_blank"
          href="https://www.facebook.com/artsandculturecenter"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/ACC.jpg"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>

        <a
          target="_blank"
          href="https://www.facebook.com/lsutingog"
          class="w-full border-gray-50 shadow-lg rounded-full hover:border-green-500 lg:border-4 text-green-950 hover:text-white"
        >
          <img
            src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/LSUTingog.jpg"
            class="lg:w-[70px] w-[100px] mx-auto rounded-full"
          />
        </a>
      </div>
    </div>

    <div class="py-5">
      <div class="">
        <p
          class="w-7/12 text-center mx-auto uppercase font-bold lg:mb-7 mb-5 lg:text-sm text-xs"
        >
          Affiliations
        </p>
        <div class="lg:w-5/12 w-11/12 mx-auto flex items-center">
          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/Lasallian%20East%20Asia%20District.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DLSP.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/CHED.png"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>

          <a href="/" class="w-fit mx-auto" target="_blank">
            <img
              src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DepEd.jpg"
              class="lg:w-16 w-10 mx-auto"
            />
          </a>
        </div>
      </div>
    </div>

    <div class="">
      <div class="lg:w-full w-11/12 mx-auto lg:pt-0 pt-3">
        <div class="lg:py-7 lg:flex lg:mt-0 mt-5">
          <div class="lg:w-9/12 lg:border-r border-green-200 px-2">
            <p
              class="w-11/12 mx-auto uppercase font-bold lg:mb-7 mb-5 lg:text-left text-center lg:text-sm text-xs"
            >
              Accreditations
            </p>
            <div
              class="grid lg:grid-cols-3 grid-cols-2 items-center mx-auto py-1 lg:gap-y-5 gap-y-5"
            >
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PAASCU-Reaccredited.png"
                  class="lg:w-[60px] w-[50px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/LSU-ISO-Cert-Mark.jpg"
                  class="lg:w-[300px] w-[200px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/DPO-SEAL.png"
                  class="lg:w-[40px] w-[40px] h-auto object-contain lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PCNC.png"
                  class="lg:w-[100px] w-[100px] h-auto lg:mx-5 mx-auto"
                />
              </a>

              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/PEAC.png"
                  class="lg:w-[100px] w-[90px] h-auto lg:mx-5 mx-auto"
                />
              </a>
              <a href="/" class="w-fit mx-auto" target="_blank">
                <img
                  src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/gradeA.png"
                  class="lg:w-[100px] w-[100px] h-auto lg:mx-5 mx-auto"
                />
              </a>
            </div>
          </div>
          <div class="lg:w-5/12 lg:px-10 lg:mt-0 mt-10 lg:mb-0 mb-4">
            <p
              class="uppercase font-bold lg:mb-7 mb-5 mx-auto lg:text-sm text-xs lg:text-left text-center"
            >
              Linkages
            </p>
            <div class="w-fit mx-auto py-1 lg:gap-y-7 gap-y-7">
              <div class="flex gap-x-10">
                <a href="/" class="w-fit mx-auto block" target="_blank">
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/partners/Philnitslogo.jpg"
                    class="lg:w-[100px] w-[80px] h-auto object-contain mx-auto"
                  />
                </a>
                <a
                  href="/linkages/Mikrotik"
                  class="w-fit mx-auto lg:mt-3 mt-1"
                  target="_blank"
                >
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/partners/MikrotikAcademy.jpg"
                    class="lg:w-[150px] w-[130px] h-auto object-contain lg:mx-5 mx-auto"
                /></a>
              </div>
              <div class="w-fit mx-auto lg:mt-10 mt-7">
                <a
                  href="/linkages/Mikrotik"
                  class="w-fit mx-auto"
                  target="_blank"
                >
                  <img
                    src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/GlobeBusiness.png"
                    class="lg:w-[100px] w-[100px] h-auto object-contain lg:mx-5 mx-auto"
                /></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style scoped>
.bg {
  background: url("https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/banners/green-tones-gradient-background_23-2148374436.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
