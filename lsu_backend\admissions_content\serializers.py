from rest_framework import serializers
from .models import NewStudentModel, DocumentFiles, NewStudentEmailNotification, AdmissionsDoneModel, AdvisingDoneModel, AccountingDoneModel, EvaluationDoneModel, EvaluationFormDataModel

class DocumentFilesSerializer(serializers.ModelSerializer):
  class Meta:
    model = DocumentFiles
    fields = '__all__'

class NewStudentSerializer(serializers.ModelSerializer):
  class Meta:
    model = NewStudentModel
    fields = '__all__'

class NewStudentEmailNotificationSerializer(serializers.ModelSerializer):
  class Meta:
    model = NewStudentEmailNotification
    fields = '__all__'

class AdmissionsDoneSerializer(serializers.ModelSerializer):
  class Meta:
    model = AdmissionsDoneModel
    fields = '__all__'

class AdvisingDoneSerializer(serializers.ModelSerializer):
  class Meta:
    model = AdvisingDoneModel
    fields = '__all__'

class AccountingDoneSerializer(serializers.ModelSerializer):
  class Meta:
    model = AccountingDoneModel
    fields = '__all__'

class EvaluationDoneSerializer(serializers.ModelSerializer):
  class Meta:
    model = EvaluationDoneModel
    fields = '__all__'

class EvaluationFormDataSerializer(serializers.ModelSerializer):
  class Meta:
    model = EvaluationFormDataModel
    fields = '__all__'