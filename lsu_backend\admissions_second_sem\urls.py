from django.urls import path, include
from . import views 
from rest_framework.routers import DefaultRouter

router = DefaultRouter()
# router.register('files', views.ReceiptDataViewSet, basename='files')
router.register('files', views.ReceiptDataViewSet)

urlpatterns = [
 path('file/list/', include(router.urls)),
 # path('file/list/files/', views.ReceiptDataViewSet.as_view()),
 path('new-tracking-id/', views.SubmitNewTrackingID.as_view()),
 path('submit-new-student-to-gmail/', views.SubmitNewStudentToGmail.as_view()),
 path('submit-new-student-to-gmail-admissions-done/', views.AdmissionsDone.as_view()),
 path('submit-new-student-to-gmail-advising-done/', views.AdvisingDone.as_view()),
 path('submit-new-student-to-gmail-accounting-done/', views.AccountingDone.as_view()),
 path('submit-new-student-to-gmail-evaluation-done/', views.EvaluationDone.as_view()),
 path('submit-new-student-to-gmail-validation-done/', views.ValidationDone.as_view()),
 path('submit-evaluation-form/list/', views.EvaluationFormDataListView.as_view()),
 path('submit-evaluation-form/<int:pk>/', views.EvaluationFormDataDetailView.as_view()),
 path('submit-evaluation-form/<int:pk>/delete/', views.EvaluationFormDataCreateView.as_view()),
 path('submit-evaluation-form/<int:pk>/edit/', views.EvaluationFormDataCreateView.as_view()),
 path('submit-evaluation-form/create/', views.EvaluationFormDataCreateView.as_view()),
 path('student-profile/list/', views.StudentProfileListView.as_view()),
 path('student-profile/<int:pk>/', views.StudentProfileDetailView.as_view()),
 path('student-profile/<int:pk>/delete/', views.StudentProfileCreateView.as_view()),
 path('student-profile/<int:pk>/edit/', views.StudentProfileCreateView.as_view()),
 path('student-profile/create/', views.StudentProfileCreateView.as_view()),
 path('list/', views.EnrolleeDataFirstSemListView.as_view()),
 path('<int:pk>/', views.EnrolleeDataFirstSemDetailView.as_view()),
 path('<int:pk>/delete/', views.EnrolleeDataFirstSemCreateView.as_view()),
 path('<int:pk>/edit/', views.EnrolleeDataFirstSemCreateView.as_view()),
 path('create/', views.EnrolleeDataFirstSemCreateView.as_view()),
 path('new-student/list/', views.EnrolleeDataFirstSemNewStudentListView.as_view()),
 path('new-student/<int:pk>/', views.EnrolleeDataFirstSemNewStudentDetailView.as_view()),
 path('new-student/<int:pk>/delete/', views.EnrolleeDataFirstSemNewStudentCreateView.as_view()),
 path('new-student/<int:pk>/edit/', views.EnrolleeDataFirstSemNewStudentCreateView.as_view()),
 path('new-student/create/', views.EnrolleeDataFirstSemNewStudentCreateView.as_view()),
 path('transferee/list/', views.EnrolleeDataFirstSemTransfereeStudentListView.as_view()),
 path('transferee/<int:pk>/', views.EnrolleeDataFirstSemTransfereeStudentDetailView.as_view()),
 path('transferee/<int:pk>/delete/', views.EnrolleeDataFirstSemTransfereeStudentCreateView.as_view()),
 path('transferee/<int:pk>/edit/', views.EnrolleeDataFirstSemTransfereeStudentCreateView.as_view()),
 path('transferee/create/', views.EnrolleeDataFirstSemTransfereeStudentCreateView.as_view()),
 path('second-degree-holder/list/', views.EnrolleeDataFirstSemSecondDegreeHolderStudentListView.as_view()),
 path('second-degree-holder/<int:pk>/', views.EnrolleeDataFirstSemSecondDegreeHolderStudentDetailView.as_view()),
 path('second-degree-holder/<int:pk>/delete/', views.EnrolleeDataFirstSemSecondDegreeHolderStudentCreateView.as_view()),
 path('second-degree-holder/<int:pk>/edit/', views.EnrolleeDataFirstSemSecondDegreeHolderStudentCreateView.as_view()),
 path('second-degree-holder/create/', views.EnrolleeDataFirstSemSecondDegreeHolderStudentCreateView.as_view()),
]