<script setup>
import { useUserStore } from "@/stores/user";
import _ from "lodash";
import moment from "moment";
import { ref, computed, watch } from "vue";
import axios from "axios";
import VueDatePicker from "@vuepic/vue-datepicker";
//   For making HTTP requests
//   import "./css/main.css";
//   import headOfficeJSON from "./head_office.json";
//   const headOffice = ref(headOfficeJSON);

// Privacy policy toggle
const showPrivacyPolicy = ref(false);

const togglePrivacyPolicy = () => {
  showPrivacyPolicy.value = !showPrivacyPolicy.value;
};

const thankYouDisplay = ref(false);

const userStore = useUserStore();
const endpoint = ref(userStore.mainDevServer);
const formDisplay = ref(true);

const dateToday = moment().format("MMMM DD, YYYY h:mm:ss A");
const digitsNum = ref(0);

const requireAllFields = ref(false);

const info = ref({
  firstname: "",
  middlename: "",
  lastname: "",
  birthdate: "",
  mother_maiden_name: "",
  contact_number: "",
  email: "",
  alumni: "",
  college: "",
  course: "",
  year_graduated_last_attended: "",
  type_document_requests: [], // Add this line
  details: "", // Add this line
  valid_id_front: [],
  valid_id_back: [],
  credential_evaluation_requests: [{
    name: 'N/A',
    url: 'https://lsu.edu.ph'
  }],
  tracking_id: "LSURHEU" + moment().valueOf(),
  data_privacy: "",
  logs: [
    {
      timestamp: dateToday,
      status_remarks: "Received. Now processing.",
    }
  ],
});

// Document request options as a dynamic array
const documentRequestOptions = ref([
  "Transcript of Records",
  "Transfer of Credentials (Honorable Dismissal)",
  "CAV (Certification, Authentication, Verification)",
  "Credential Evaluations (WES, CGFNS, NCLEX, SpanTran, IES, etc.)",
]);

// Document requests
const showOtherDocumentField = ref(false);
const otherDocumentRequest = ref("");
const documentRequestError = ref(false);

// Watch for changes to the "Other" checkbox and otherDocumentRequest
watch(
  [showOtherDocumentField, otherDocumentRequest],
  ([showField, otherValue]) => {
    // Remove any previous "Other: something" entries
    info.value.type_document_requests =
      info.value.type_document_requests.filter(
        (item) => !item.startsWith("Other:")
      );

    // Add the new "Other: something" entry if applicable
    if (showField && otherValue.trim()) {
      info.value.type_document_requests.push(`Other: ${otherValue.trim()}`);
    }
  }
);

// Calculate min date (90 years ago from today)
const minBirthDate = computed(() => {
  const today = new Date();
  const minDate = new Date(today);
  minDate.setFullYear(today.getFullYear() - 90);
  return minDate.toISOString().split("T")[0]; // Format as YYYY-MM-DD
});

// Calculate max date (15 years ago from today)
const maxBirthDate = computed(() => {
  const today = new Date();
  const maxDate = new Date(today);
  maxDate.setFullYear(today.getFullYear() - 15);
  return maxDate.toISOString().split("T")[0]; // Format as YYYY-MM-DD
});

// Alternative implementation using moment.js if you prefer
// const minBirthDate = computed(() => moment().subtract(90, 'years').format('YYYY-MM-DD'));
// const maxBirthDate = computed(() => moment().subtract(15, 'years').format('YYYY-MM-DD'));

const invalidEmail = ref(false);

const validateEmail = () => {
  if (!info.value.email) return false;
  const pattern = /^[a-zA-Z0-9._-]+@(gmail\.com|lsu\.edu\.ph)$/;
  return pattern.test(info.value.email);
};

const invalidContactNumber = ref(false);

const validateContactNumber = () => {
  if (!info.value.contact_number) return false;
  const pattern = /^(\+63|\+65)[0-9]{9,10}$/;
  return pattern.test(info.value.contact_number);
};

// info.value.graduatedFromLSU !== null &&
// info.value.graduatedFromLSU !== undefined &&
// Check if all required fields are filled
const isFormValid = computed(() => {
  const hasRequiredFields = (
    info.value.firstname &&
    info.value.middlename &&
    info.value.lastname &&
    info.value.birthdate &&
    info.value.mother_maiden_name &&
    info.value.contact_number &&
    info.value.email &&
    info.value.alumni &&
    info.value.college &&
    info.value.course &&
    info.value.year_graduated_last_attended &&
    info.value.data_privacy &&
    validateContactNumber() &&
    validateEmail() &&
    info.value.type_document_requests.length > 0 && // At least one document request
    (!showOtherDocumentField.value ||
      (showOtherDocumentField.value &&
        otherDocumentRequest.value.trim() !== ""))
  );
  
  // Check if files are selected or already uploaded
  const hasFilesSelectedOrUploaded = (
    (selectedFilesFront.value.length > 0 || uploadedFilesFront.value.length > 0) &&
    (selectedFilesBack.value.length > 0 || uploadedFilesBack.value.length > 0) &&
    (selectedFilesCredential.value.length > 0 || uploadedFilesCredential.value.length > 0)
  );
  
  return hasRequiredFields && hasFilesSelectedOrUploaded;
});

// Add these refs to track upload status
const isUploading = ref(false);
const allUploadsComplete = ref(false);

// Add a flag to track submission status
const isSubmitting = ref(false);

const uploadFiles = async () => {
  // Check if files are selected but not yet uploaded
  const hasUnuploadedFiles = (
    selectedFilesFront.value?.length > 0 || 
    selectedFilesBack.value?.length > 0 || 
    selectedFilesCredential.value?.length > 0
  );
  

  if (hasUnuploadedFiles) {
    isUploading.value = true;
    
    try {
      // Upload all files first
      await Promise.all([
        uploadFilesFront(),
        uploadFilesBack(),
        uploadFilesCredential()
      ]);


      postAPI();
      
      isUploading.value = false;
    } catch (error) {
      console.error("Error uploading files:", error);
      isUploading.value = false;
      // Show error message to user
      alert("There was an error uploading your files. Please try again.");
      isSubmitting.value = false; // Reset submission flag on error
    }
  }
}

const submitForm = () => {
  uploadFiles();

}

const postAPI = async () => {
  await $fetch(endpoint.value + "/api/registrar/create/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: info.value,
  }).then((response) => {
    console.log(response)
    formDisplay.value = false;
    thankYouDisplay.value = true;
    submitConfirmationEmail();
  })
}

const submitConfirmationEmail = async () => {
  await $fetch(endpoint.value + "/api/registrar/submit-appointment-to-gmail/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: info.value,
  }).then((response) => {
    console.log(response);
  });
};

const collegeList = ref([
  "Arts and Sciences, Engineering, Architecture, Computer Studies",
  "Business-Related Courses, BSTM",
  "Nursing, and Graduate Studies",
  "Education Courses, BSHM",
  "Criminology",
]);

const courseList = ref([
  "Bachelor of Science in Business Administration (BSBA)",
  "Bachelor of Science in Accounting Information System (BSAIS)",
  "Bachelor of Science in Accountancy (BSAc)",
  "Bachelor of Science in Civil Engineering (BSCE)",
  "Bachelor of Science in Architecture (BSArch)",
  // Add more courses as needed
]);

const showAddNewCourse = ref(false);
const newCourse = ref("");

// Handle course selection change
const handleCourseChange = () => {
  if (info.value.course === "add_new") {
    showAddNewCourse.value = true;
    info.value.course = ""; // Clear the dropdown value
  } else {
    // Ensure the selected course is stored as a string
    info.value.course = String(info.value.course);
  }
};

// Add the new course to the list
const addNewCourse = () => {
  if (newCourse.value.trim()) {
    const courseString = newCourse.value.trim();
    courseList.value.push(courseString);
    info.value.course = courseString; // Store as string
    newCourse.value = "";
    showAddNewCourse.value = false;
  }
};

// Cancel adding new course
const cancelAddNew = () => {
  showAddNewCourse.value = false;
  newCourse.value = "";
  // Reset to first course in list if available
  if (courseList.value.length > 0) {
    info.value.course = String(courseList.value[0]);
  } else {
    info.value.course = "";
  }
};

// File upload for Government ID (Front)
const selectedFilesFront = ref([]);
const uploadedFilesFront = ref([]);
const uploadStatusFront = ref(null);

// File upload for Government ID (Back)
const selectedFilesBack = ref([]);
const uploadedFilesBack = ref([]);
const uploadStatusBack = ref(null);

// File upload for Credential Evaluations
const selectedFilesCredential = ref([]);
const uploadedFilesCredential = ref([]);
const uploadStatusCredential = ref(null);

// Handle file upload for Government ID (Front)
const handleFileUploadFront = (event) => {
  selectedFilesFront.value = Array.from(event.target.files);
  console.log("Front ID files:", event);
  // Convert FileList to an array
  selectedFilesFront.value.forEach((file) => {
    console.log(`Front ID File: ${file.name}, Size: ${file.size} bytes`);
  });
};

// Handle file upload for Government ID (Back)
const handleFileUploadBack = (event) => {
  selectedFilesBack.value = Array.from(event.target.files);
  console.log("Back ID files:", event);
  // Convert FileList to an array
  selectedFilesBack.value.forEach((file) => {
    console.log(`Back ID File: ${file.name}, Size: ${file.size} bytes`);
  });
};

// Handle file upload for Credential Evaluations
const handleFileUploadCredential = (event) => {
  selectedFilesCredential.value = Array.from(event.target.files);
  console.log("Credential files:", event);
  // Convert FileList to an array
  selectedFilesCredential.value.forEach((file) => {
    console.log(`Credential File: ${file.name}, Size: ${file.size} bytes`);
  });
};

// Upload Government ID (Front) files
const uploadFilesFront = async () => {
  if (!selectedFilesFront.value.length) {
    return;
  }
  const formData = new FormData();
  selectedFilesFront.value.forEach((file) => {
    formData.append("file", file);
  });
  try {
    uploadStatusFront.value = "Uploading front ID...";
    const response = await axios.post(
      endpoint.value + "/api/registrar/upload/",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    uploadStatusFront.value = "Upload successful!";
    console.log("Front ID files uploaded:", response.data);
    
    // Handle both array and single object responses
    if (Array.isArray(response.data)) {
      uploadedFilesFront.value = response.data.map((item) => ({
        name: item.name || item.file.split('/').pop(),
        url: item.url || item.file,
      }));
    } else {
      // Handle single object response
      uploadedFilesFront.value = [{
        name: response.data.file.split('/').pop(),
        url: response.data.file,
      }];
    }
    
    selectedFilesFront.value = []; // Clear selected files after successful upload
    info.value.valid_id_front = uploadedFilesFront.value;
    console.log("Updated info with front ID:", info.value);
  } catch (error) {
    console.error("Front ID upload error:", error);
    uploadStatusFront.value = "Upload failed: " + error.message;
  }
};

// Upload Government ID (Back) files
const uploadFilesBack = async () => {
  if (!selectedFilesBack.value.length) {
    return;
  }
  const formData = new FormData();
  selectedFilesBack.value.forEach((file) => {
    formData.append("file", file);
  });
  try {
    uploadStatusBack.value = "Uploading back ID...";
    const response = await axios.post(
      endpoint.value + "/api/registrar/upload/",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    uploadStatusBack.value = "Upload successful!";
    console.log("Back ID files uploaded:", response.data);
    
    // Handle both array and single object responses
    if (Array.isArray(response.data)) {
      uploadedFilesBack.value = response.data.map((item) => ({
        name: item.name || item.file.split('/').pop(),
        url: item.url || item.file,
      }));
    } else {
      // Handle single object response
      uploadedFilesBack.value = [{
        name: response.data.file.split('/').pop(),
        url: response.data.file,
      }];
    }
    
    selectedFilesBack.value = []; // Clear selected files after successful upload
    info.value.valid_id_back = uploadedFilesBack.value;
    console.log("Updated info with back ID:", info.value);
  } catch (error) {
    console.error("Back ID upload error:", error);
    uploadStatusBack.value = "Upload failed: " + error.message;
  }
};

// Upload Credential Evaluations files
const uploadFilesCredential = async () => {
  if (!selectedFilesCredential.value.length) {
    return;
  }
  const formData = new FormData();
  selectedFilesCredential.value.forEach((file) => {
    formData.append("file", file);
  });
  try {
    uploadStatusCredential.value = "Uploading credentials...";
    const response = await axios.post(
      endpoint.value + "/api/registrar/upload/",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    uploadStatusCredential.value = "Upload successful!";
    console.log("Credential files uploaded:", response.data);
    
    // Handle both array and single object responses
    if (Array.isArray(response.data)) {
      uploadedFilesCredential.value = response.data.map((item) => ({
        name: item.name || item.file.split('/').pop(),
        url: item.url || item.file,
      }));
    } else {
      // Handle single object response
      uploadedFilesCredential.value = [{
        name: response.data.file.split('/').pop(),
        url: response.data.file,
      }];
    }
    
    selectedFilesCredential.value = []; // Clear selected files after successful upload
    info.value.credential_evaluation_requests = uploadedFilesCredential.value;
    console.log("Updated info with credentials:", info.value);
  } catch (error) {
    console.error("Credential upload error:", error);
    uploadStatusCredential.value = "Upload failed: " + error.message;
  }
};
</script>
<template>
  <div class="bg-gray-50">
    <Header />
    <div class="">
      <div class="relative">
        <div class="bg-green-700 lg:h-[200px] h-[130px]">
          <div
            class="lg:pt-10 absolute top-1/2 transform -translate-y-1/2 w-full"
          >
            <p
              class="font-bold uppercase text-white lg:text-2xl text-sm w-11/12 mx-auto"
            >
              REGISTRAR
            </p>
            <p class="text-xs w-11/12 mx-auto text-white">
              Higher Education Registrar Appointment
            </p>
          </div>
        </div>

        <div class="shadow-lg text-green-700">
          <div class="lg:flex justify-between border-b border-gray-200 lg:pl-5">
            <div
              class="flex items-center capitalize text-xs lg:border-b-0 border-b lg:px-0 px-1.5 py-2"
            >
              <div>
                <a href="/registrar" class="mr-2 hover:underline lg:h-10"
                  >Home</a
                >
              </div>
              <div>
                <i class="fas fa-caret-right"></i>
                <a
                  href="/registrar/heu/appointment"
                  class="mx-2 hover:underline lg:h-10"
                  >HEU Appointment</a
                >
              </div>
            </div>
            <div class="flex hover:text-green-800 text-white bg-white h-full">
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
              >
                <a href="/registrar" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-video-camera" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Demo Guide</span>
                </a>
              </div>
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full border-r"
              >
                <a href="/registrar/track" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-universal-access" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Track</span>
                </a>
              </div>
              <div
                class="hover:bg-green-800 border-x bg-white hover:text-white text-green-800 px-1 lg:px-4 lg:h-10 h-8 flex items-center capitalize text-xs lg:py-2 py-1 lg:w-fit w-full"
              >
                <a href="/registrar/login" class="flex items-center w-fit mx-auto">
                  <i class="fa fa-user" aria-hidden="true"></i>
                  <span class="ml-3 whitespace-nowrap">Admin Login</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="formDisplay" class="">
      <div
        class="header bg-gradient-to-b from-[#fefefe] via-[#fefefe] to-[#bce3c2] lg:pt-5 pt-1"
      >
        <div class="lg:w-9/12 w-11/12 mx-auto bg-white">
       
            <!-- <form v-on:submit.prevent="submitForm" class=""> -->
            <div>
              <div class="border-2 border-green-700 shadow-lg my-3">
                <div class="">
                  <h2
                    class="lg:text-base text-sm px-3 uppercase py-1.5 font-bold bg-green-900 text-white text-center tracking-wide"
                  >
                    Request Appointment Form
                    <!-- <span class="font-light text-xs bg-green-900 text-white block">
                      {{ info.document_code }}</span> -->
                  </h2>
                  <div>
                    <div class="py-4 border-b-2 px-2">
                      <p class="text-green-900 text-xs font-bold lg:text-center">
                        Maayong La Salle!
                      </p>
                      <div class="">
                        <p
                          class="text-green-900 text-xs lg:text-center lg:w-9/12 mx-auto"
                        >
                          Please use this form in requesting documents at La Salle
                          University Higher Education Registrar. By leveraging
                          this form, graduates can conveniently request various
                          documents online, eliminating the need for in-person
                          appointments at LSU.
                        </p>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="w-fit mx-auto text-xs mt-4 px-4 font-montserrat tracking-tight"> DRS No. <span class="border-b px-1">{{ info.tracking_id }}</span></div> -->
                  <div class="lg:p-5 px-2 pt-3 pb-2 gap-3">
                    <div class="w-full lg:mb-0 mb-5">
                      <div class="w-full gap-3">
                        <div class="gap-3 w-full">
                          <div class="gap-3 lg:mb-2 shadow py-2 px-2">
                            <div class="lg:gap-x-2 gap-x-1 w-full">
                              <div class="lg:flex gap-x-2">
                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Fullname
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full flex lg:gap-x-2 gap-x-1">
                                    <input
                                      type="text"
                                      class="lg:px-2 px-1 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      placeholder="First Name"
                                      v-model="info.firstname"
                                      required
                                    />
                                    <input
                                      type="text"
                                      class="lg:px-2 px-1 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      placeholder="Middle Name"
                                      v-model="info.middlename"
                                      required
                                    />
                                    <input
                                      type="text"
                                      class="lg:px-2 px-1 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      placeholder="Last Name"
                                      v-model="info.lastname"
                                      required
                                    />
                                  </div>
                                </div>

                                <div class="lg:w-fit w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Date of Birth
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div
                                    class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                  >
                                    <input
                                      type="date"
                                      class="px-1 w-full border-t-0 border-x-0 border-green-700 lg:h-[34px] h-8 text-xs"
                                      placeholder="Date of Birth"
                                      :min="minBirthDate"
                                      :max="maxBirthDate"
                                      v-model="info.birthdate"
                                      required
                                    />
                                  </div>
                                </div>
                              </div>

                              <div class="lg:flex gap-x-2">
                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Mother's Full Maiden Name
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div
                                    class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                  >
                                    <input
                                      type="text"
                                      class="px-2 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                      placeholder="Mother's Full Maiden Name"
                                      v-model="info.mother_maiden_name"
                                      required
                                    />
                                  </div>
                                </div>

                                <div class="lg:w-6/12 w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Contact Number
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div
                                    class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                  >
                                    <input
                                      type="tel"
                                      class="px-2 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                      placeholder="e.g. +639210689089"
                                      v-model="info.contact_number"
                                      pattern="^(\+63|\+65)[0-9]{9,10}$"
                                      maxlength="13"
                                      title="Please enter a valid phone number with country code +63 or +65"
                                      required
                                    />
                                  </div>
                                  <p
                                    v-if="invalidContactNumber"
                                    class="text-xs text-red-700 mt-2 px-1"
                                  >
                                    Please enter a valid phone number with country
                                    code +63
                                  </p>
                                </div>

                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Email
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>

                                  <div
                                    class="w-full flex items-center gap-x-1 bg-white border-b-2 border-green-700 shadow-lg rounded-sm h-fit"
                                  >
                                    <!-- <img src="https://lsu-media-styles.sgp1.digitaloceanspaces.com/lsu-public-images/banners/logo/Google_Drive.png" 
                                      class="h-5 w-5 ml-2" /> -->
                                    <!-- pattern="^[a-zA-Z0-9._-]+@(gmail\.com|lsu\.edu\.ph)$"
                                      title="Please enter a valid Gmail or LSU email address" -->
                                    <input
                                      type="email"
                                      class="px-2 w-full border-t-0 border-x-0 border-green-700 lg:h-9 h-8 text-xs py-2"
                                      placeholder="e.g. <EMAIL>"
                                      v-model="info.email"
                                      required
                                      title="Please enter a valid Email Address"
                                    />
                                  </div>
                                  <!-- <p v-if="invalidEmail" class="text-xs text-red-700 mt-2 px-1">Only Gmail or LSU email addresses are accepted.</p> -->
                                  <p
                                    v-if="invalidEmail"
                                    class="text-xs text-red-700 mt-2 px-1"
                                  >
                                    Only Valid email addresses are accepted.
                                  </p>
                                </div>
                              </div>

                              <div class="lg:flex gap-x-2">
                                <div class="lg:w-fit mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Did you graduate in ICC/LSU?
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <div
                                      class="flex px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                    >
                                      <div class="w-fit mx-auto flex">
                                        <div
                                          class="flex gap-x-2 items-center w-[70px] justify-center"
                                        >
                                          <span>
                                            <input
                                              type="radio"
                                              value="yes"
                                              v-model="info.alumni"
                                              class="mr-1"
                                              required
                                              id="yes"
                                            />
                                          </span>
                                          <label
                                            class="lg:text-sm text-xs hover:font-bold"
                                            for="yes"
                                          >
                                            Yes
                                          </label>
                                        </div>
                                        <div
                                          class="flex gap-x-2 items-center w-[70px] justify-center"
                                        >
                                          <span>
                                            <input
                                              type="radio"
                                              value="no"
                                              v-model="info.alumni"
                                              class="mr-1"
                                              required
                                              id="no"
                                            />
                                          </span>
                                          <label
                                            class="lg:text-sm text-xs hover:font-bold"
                                            for="no"
                                          >
                                            No
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      College
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <select
                                      v-model="info.college"
                                      class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      required
                                    >
                                      <option value="" disabled selected>
                                        Choose
                                      </option>
                                      <option
                                        v-for="(college, index) in collegeList"
                                        :key="index"
                                        :value="college"
                                      >
                                        {{ college }}
                                      </option>
                                    </select>
                                  </div>
                                </div>
                              </div>

                              <div class="lg:flex gap-x-2">
                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Course
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div> 
                                    <input
                                      type="text"
                                      class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      placeholder="Course"
                                      v-model="info.course"
                                      required
                                    />
                                  </div>
                                  <!-- <div class="w-full">
                                    <div v-if="!showAddNewCourse">
                                      <select
                                        v-model="info.course"
                                        @change="handleCourseChange"
                                        class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                        required
                                      >
                                        <option value="" disabled selected>
                                          Choose
                                        </option>
                                        <option
                                          v-for="(course, index) in courseList"
                                          :key="index"
                                          :value="course"
                                        >
                                          {{ course }}
                                        </option>
                                        <option value="add_new">+ Other</option>
                                      </select>
                                    </div>
                                    <div v-else class="flex flex-col gap-2">
                                      <input
                                        v-model="newCourse"
                                        type="text"
                                        class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                        placeholder="Enter new course"
                                        required
                                      />
                                      <div class="flex gap-2">
                                        <div
                                          @click="addNewCourse"
                                          class="text-xs bg-green-700 text-white px-2 py-1 rounded"
                                        >
                                          <i class="fa fa-check"></i>
                                        </div>
                                        <div
                                          @click="cancelAddNew"
                                          class="text-xs border border-green-700 text-green-700 px-2 py-1 rounded"
                                        >
                                          Cancel
                                        </div>
                                      </div>
                                    </div>
                                  </div> -->
                                </div>

                                <div class="lg:w-fit mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Year Graduated or Last Attended
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div class="w-full">
                                    <select
                                      class="px-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                      required
                                      v-model="info.year_graduated_last_attended"
                                    >
                                      <option value="" disabled selected>
                                        Select Year
                                      </option>
                                      <option
                                        v-for="year in _.range(
                                          moment().year(),
                                          1930,
                                          -1
                                        )"
                                        :key="year"
                                        :value="year"
                                      >
                                        {{ year }}
                                      </option>
                                    </select>
                                  </div>
                                </div>
                              </div>

                              <div class="lg:flex gap-x-2">
                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">
                                      Type of Document Requests
                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </div>
                                  </label>
                                  <div
                                    class="w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm p-2"
                                  >
                                    <div class="space-y-2">
                                      <div
                                        v-for="(
                                          document, index
                                        ) in documentRequestOptions"
                                        :key="index"
                                        class="flex items-center"
                                      >
                                        <input
                                          type="checkbox"
                                          :id="'doc_' + index"
                                          :value="document"
                                          v-model="info.type_document_requests"
                                          class="mr-2"
                                        />
                                        <label
                                          :for="'doc_' + index"
                                          class="text-xs"
                                          >{{ document }}</label
                                        >
                                      </div>

                                      <div class="flex items-center">
                                        <input
                                          type="checkbox"
                                          id="other_doc"
                                          value="other"
                                          v-model="showOtherDocumentField"
                                          class="mr-2"
                                        />
                                        <label for="other_doc" class="text-xs"
                                          >Other:</label
                                        >

                                        <input
                                          v-if="showOtherDocumentField"
                                          type="text"
                                          v-model="otherDocumentRequest"
                                          class="ml-2 px-2 py-1 border-b border-green-700 bg-transparent text-xs w-full"
                                          placeholder="Please specify"
                                        />
                                      </div>
                                    </div>

                                    <p
                                      v-if="documentRequestError"
                                      class="text-xs text-red-700 mt-2"
                                    >
                                      Please select at least one document type
                                    </p>
                                  </div>
                                </div>

                                <div class="w-full mb-2">
                                  <label
                                    class="lg:text-xs text-[10px] text-green-950 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                  >
                                    <div class="">Purpose</div>
                                  </label>
                                  <div class="w-full">
                                    <textarea
                                      class="px-2 py-2 box-border w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm text-xs"
                                      placeholder="Purpose"
                                      v-model="info.details"
                                      rows="7"
                                      cols="50"
                                    ></textarea>
                                  </div>
                                </div>
                              </div>

                              <div class="lg:mt-7 mt-3 mb-4 bg-gray-50">
                                <div
                                  class="lg:text-center text-green-900 font-bold uppercase lg:mb-3 mb-1 pt-3 flex items-center lg:justify-center px-2"
                                >
                                  <i
                                    class="fa fa-file mr-2"
                                    aria-hidden="true"
                                  ></i>
                                  Upload Documents
                                </div>
                                <div
                                  class="lg:flex gap-x-2 lg:pb-5 lg:pt-2 lg:px-5 px-2 bg-[#fafafa]"
                                >
                                  <!-- Government ID (Front) -->
                                  <div class="w-full mb-2">
                                    <label
                                      class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                    >
                                      <div class="">
                                        Government Issue ID (Front)
                                        <span class="text-red-600 font-normal text-sm">*</span>
                                      </div>
                                    </label>
                                    <div class="w-full">
                                      <input
                                        type="file"
                                        class="lg:px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                        @change="handleFileUploadFront"
                                        id="file-upload-front"
                                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                      />

                                      <div v-if="uploadStatusFront">
                                        {{ uploadStatusFront }}
                                      </div>
                                      <div
                                        v-for="file in uploadedFilesFront"
                                        :key="file.url"
                                      >
                                        <img
                                          v-if="
                                            file.url.includes('jpg') ||
                                            file.url.includes('png')
                                          "
                                          :src="file.url"
                                          alt="Uploaded Front ID"
                                          class="lg:w-80 w-full"
                                        />

                                        <!-- <a
                                          :href="file.url"
                                          target="_blank"
                                          rel="noopener noreferrer"
                                        >{{ file.name }}</a> -->
                                      </div>

                                      <div
                                        v-for="file in selectedFilesFront"
                                        :key="file.name"
                                        class="my-4 shadow-lg px-2 py-3 border-2"
                                      >
                                        <!-- <a
                                          href="#"
                                          class="tracking-tighter text-xs"
                                        >{{ file.name }}</a> -->

                                        <span class="font-bold block">
                                          {{ (file.size / 1024 / 1024).toFixed(2) }}
                                          MB size
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Government ID (Back) -->
                                  <div class="w-full mb-2">
                                    <label
                                      class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                    >
                                      <div class="">
                                        Government Issue ID (Back)
                                        <span class="text-red-600 font-normal text-sm">*</span>
                                      </div>
                                    </label>
                                    <div class="w-full">
                                      <input
                                        type="file"
                                        class="lg:px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                        @change="handleFileUploadBack"
                                        id="file-upload-back"
                                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                      />
                                      
                                      <div v-if="uploadStatusBack">
                                        {{ uploadStatusBack }}
                                      </div>
                                      <div
                                        v-for="file in uploadedFilesBack"
                                        :key="file.url"
                                      >
                                        <img
                                          v-if="
                                            file.url.includes('jpg') ||
                                            file.url.includes('png')
                                          "
                                          :src="file.url"
                                          alt="Uploaded Back ID"
                                          class="lg:w-80 w-full"
                                        />

                                        <!-- <a
                                          :href="file.url"
                                          target="_blank"
                                          rel="noopener noreferrer"
                                        >{{ file.name }}</a> -->
                                      </div>

                                      <div
                                        v-for="file in selectedFilesBack"
                                        :key="file.name"
                                        class="my-4 shadow-lg px-2 py-3 border-2"
                                      >
                                        <!-- <a
                                          href="#"
                                          class="tracking-tighter text-xs"
                                        >{{ file.name }}</a> -->

                                        <span class="font-bold block">
                                          {{ (file.size / 1024 / 1024).toFixed(2) }}
                                          MB size
                                        </span>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Credential Evaluations -->
                                  <div class="w-full mb-2">
                                    <label
                                      class="lg:text-xs text-[10px] text-green-800 pb-2 font-bold whitespace-nowrap lg:w-6/12"
                                    >
                                      <div class="">
                                        Credential Evaluations Requests
                                       
                                      </div>
                                    </label>
                                    <div class="w-full">
                                      <input
                                        type="file"
                                        class="lg:px-2 py-2 w-full border-b-2 border-t-0 border-x-0 border-green-700 shadow-lg rounded-sm lg:h-9 h-8 text-xs"
                                        @change="handleFileUploadCredential"
                                        id="file-upload-credential"
                                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                                      />
                                      
                                      <div v-if="uploadStatusCredential">
                                        {{ uploadStatusCredential }}
                                      </div>
                                      <div
                                        v-for="file in uploadedFilesCredential"
                                        :key="file.url"
                                      >
                                        <img
                                          v-if="
                                            file.url.includes('jpg') ||
                                            file.url.includes('png')
                                          "
                                          :src="file.url"
                                          alt="Uploaded Credential"
                                          class="lg:w-80 w-full"
                                        />

                                        <!-- <a
                                          :href="file.url"
                                          target="_blank"
                                          rel="noopener noreferrer"
                                        >{{ file.name }}</a> -->
                                      </div>

                                      <div
                                        v-for="file in selectedFilesCredential"
                                        :key="file.name"
                                        class="my-4 shadow-lg px-2 py-3 border-2"
                                      >
                                        <!-- <a
                                          href="#"
                                          class="tracking-tighter text-xs"
                                        >{{ file.name }}</a> -->

                                        <span class="font-bold block">
                                          {{ (file.size / 1024 / 1024).toFixed(2) }}
                                          MB size
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="mt-7 lg:mb-4">
                                <div
                                  @click="togglePrivacyPolicy"
                                  class="text-green-800 px-2 py-1 bg-gray-50 font-bold text-center text-sm cursor-pointer transition-colors"
                                >
                                  <p>
                                    PRIVACY POLICY
                                    <span class="text-xs ml-1">{{
                                      showPrivacyPolicy ? "▲" : "▼"
                                    }}</span>
                                  </p>
                                </div>

                                <div
                                  v-if="showPrivacyPolicy"
                                  class="border border-gray-300 lg:p-4 p-2 bg-white text-xs"
                                >
                                  <p class="font-bold text-green-900 mb-2">
                                    PRIVACY NOTICE
                                  </p>
                                  <p class="mb-2">
                                    At the
                                    <span class="text-green-800 font-semibold"
                                      >La Salle University Registrar's
                                      Office</span
                                    >, we are committed to protecting the privacy
                                    and security of your personal information.
                                    This Privacy Notice explains how we collect,
                                    use, disclose, and protect your information
                                    when you interact with our office.
                                  </p>

                                  <p class="font-bold text-green-800 mt-4 mb-1">
                                    INFORMATION WE COLLECT
                                  </p>
                                  <p class="mb-1">
                                    We collect various types of personal
                                    information necessary for providing our
                                    services, including but not limited to:
                                  </p>
                                  <ol
                                    class="list-decimal lg:ml-6 ml-3 mb-3 space-y-1"
                                  >
                                    <li>
                                      <span class="font-semibold"
                                        >Contact Information:</span
                                      >
                                      Name, address, email address, phone number,
                                      and other contact details
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Identification Information:
                                      </span>
                                      Student ID number, government-issued
                                      identification details,
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Academic Information:</span
                                      >
                                      Course enrollment, grades, academic
                                      progress, and transcripts.
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Co-curricular Information:</span
                                      >
                                      Service learnings, outreach activities,
                                      Field Trips, Internship or apprenticeship
                                      compliance.
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Financial Information:
                                      </span>
                                      Payment details, financial aid information,
                                      promissory notes.
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Pictures and Videos
                                      </span>
                                      of activities you participate in, via
                                      official documentation of such activities.
                                    </li>
                                    <li>
                                      <span class="font-semibold"
                                        >Other Information:</span
                                      >
                                      Any additional information you provide to us
                                      in the course of our interactions.
                                    </li>
                                  </ol>

                                  <p class="font-bold text-green-800 mt-4 mb-1">
                                    HOW WE COLLECT YOUR INFORMATION
                                  </p>
                                  <p class="mb-1">
                                    We use the information we collect for the
                                    following purposes:
                                  </p>
                                  <ol
                                    class="list-decimal lg:ml-6 ml-3 mb-3 space-y-1"
                                  >
                                    <li>
                                      Providing Registrar services, including
                                      enrollment, course registration, and
                                      academic records management;
                                    </li>
                                    <li>
                                      Communicating with you regarding
                                      administrative matters, such as changes to
                                      policies or procedures;
                                    </li>
                                    <li>
                                      Processing financial transactions related to
                                      your academic activities;
                                    </li>
                                    <li>
                                      Analyzing and improving our services,
                                      systems, and operations;
                                    </li>
                                    <li>
                                      Complying with legal obligations and
                                      regulatory requirements;
                                    </li>
                                    <li>
                                      Soliciting your participation in research
                                      and non-commercial surveys sanctioned by the
                                      University;
                                    </li>
                                    <li>
                                      Sharing of grades between and among academic
                                      administrators and offices, for academic
                                      deliberations and evaluation of student
                                      performance;
                                    </li>
                                  </ol>

                                  <p class="font-bold text-green-800 mt-4 mb-1">
                                    DISCLOSURE OF YOUR INFORMATION
                                  </p>
                                  <p class="mb-1">
                                    We may disclose your personal information in
                                    the following circumstances:
                                  </p>
                                  <ol
                                    class="list-decimal lg:ml-6 ml-3 mb-3 space-y-1"
                                  >
                                    <li>
                                      To authorized personnel within La Salle
                                      University who require access to fulfill
                                      their duties.
                                    </li>
                                    <li>
                                      To educational institutions or organizations
                                      as required to facilitate academic processes
                                      (e.g., transcript requests, enrollment
                                      verification).
                                    </li>
                                    <li>
                                      To third-party service providers who assist
                                      us in carrying out our functions and
                                      services.
                                    </li>
                                    <li>
                                      When required by law or legal process, such
                                      as in response to a subpoena or court order.
                                    </li>
                                    <li>
                                      In emergencies or situations involving the
                                      health or safety of individuals.
                                    </li>
                                    <li>
                                      To Government agencies for legitimate
                                      planning purposes directly related to
                                      education and student welfare. Such purposes
                                      may include enrollment forecasting, program
                                      evaluation, research studies, and policy
                                      development
                                    </li>
                                  </ol>

                                  <p class="font-bold text-green-800 mt-4 mb-1">
                                    DATA SECURITY
                                  </p>
                                  <p class="mb-2">
                                    We implement appropriate technical and
                                    organizational measures to protect your
                                    personal information against unauthorized
                                    access, disclosure, alteration, or
                                    destruction.
                                  </p>

                                  <p class="font-bold text-green-800 mt-4 mb-1">
                                    CONTACT US
                                  </p>
                                  <p class="mb-3">
                                    If you have any questions, concerns, or
                                    complaints about our Privacy Notice or our
                                    handling of your personal information, please
                                    contact us at
                                    <span class="text-blue-600"
                                      ><EMAIL></span
                                    >
                                  </p>
                                </div>

                                <div
                                  class="border border-gray-300 lg:p-4 p-2 bg-white text-xs"
                                  :class="{ 'border-t-0': showPrivacyPolicy }"
                                >
                                  <div class="flex items-center">
                                    <p for="privacy_agreement" class="text-xs">
                                      By checking the box below, you agree with
                                      the

                                      <span
                                        @click="togglePrivacyPolicy"
                                        class="hover:uppercase cursor-pointer underline text-blue-800"
                                      >
                                        Privacy Policy
                                      </span>

                                      <span
                                        class="text-red-600 font-normal text-sm"
                                        >*</span
                                      >
                                    </p>
                                  </div>
                                  <div class="flex items-center gap-x-1">
                                    <span>
                                      <input
                                        type="checkbox"
                                        id="privacy_agreement"
                                        v-model="info.data_privacy"
                                        class="mt-1"
                                        required
                                        value="I agree"
                                      />
                                    </span>
                                    <span>
                                      <label
                                        for="privacy_agreement"
                                        :class="
                                          info.data_privacy ? 'font-bold' : ''
                                        "
                                      >
                                        I agree
                                      </label>
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="requireAllFields"
                    class="my-10 w-11/12 mx-auto text-white bg-red-800 text-center py-2 px-5 block lg:text-sm text-xs"
                  >
                    All fields are required!
                  </div>
                  <div class="pb-5 lg:px-5 px-3 mb-1">
                    <div
                      @click.prevent="submitForm"
                      :disabled="!isFormValid || isUploading"
                      class="px-10 lg:rounded-lg rounded-md text-center font-bold 
                      py-1.5 lg:w-fit w-full mx-auto block uppercase border-2 hover:bg-white  hover:text-green-900 lg:text-sm text-xs justify-center shadow-xl"
                      :class="!isFormValid  ? 'bg-[#0d6d28] text-white border-[#0d6d28]':'bg-green-900 text-white  border-[#10561c]'"
                    >
                      <span v-if="isUploading" class="justify-center">
                        <i class="fa fa-spinner fa-spin mr-2"></i>Uploading files...
                      </span>
                      <span v-else class="justify-center">
                        <i class="fa fa-paper-plane mr-2" aria-hidden="true"></i> Submit
                      </span>
                    </div>

                    <!-- Optional: Add a progress indicator for uploads -->
                    <div v-if="isUploading" class="mt-2 text-center text-sm text-green-800">
                      Please wait while your files are being uploaded...
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </div>
        <!--Waves Container-->
        <div>
          <svg
            class="waves"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            viewBox="0 24 150 28"
            preserveAspectRatio="none"
            shape-rendering="auto"
          >
            <defs>
              <path
                id="gentle-wave"
                d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z"
              />
            </defs>
            <g class="parallax">
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="0"
                fill="rgba(255,255,255,0.7"
              />
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="3"
                fill="rgba(255,255,255,0.5)"
              />
              <use
                xlink:href="#gentle-wave"
                x="48"
                y="5"
                fill="rgba(255,255,255,0.3)"
              />
              <use xlink:href="#gentle-wave" x="48" y="7" fill="#fff" />
            </g>
          </svg>
        </div>
        <!--Waves end-->
      </div>
    </div>
    <div v-if="thankYouDisplay" class="">
      <div
        class="lg:flex gap-10 lg:rounded-4xl bg-white lg:px-14 px-3 py-1 lg:w-fit w-full mx-auto lg:my-10 shadow-sm"
      >
        <div class="flex items-center">
          <img
            src="https://raw.githubusercontent.com/jorenlee/lsu-public-images/main/images/images/icons/check-mark-icon-isolated-on-white-background-vector-26464923.jpg"
            class="lg:w-44 w-20 mx-auto lg:mt-0 mt-14"
          />
        </div>
        <div
          class="text-xl text-green-900 text-center w-fit mx-auto lg:py-20 py-5"
        >
          <h1 class="font-bold text-3xl">Thanks for submitting!</h1>
          <p class="font-light pt-3 pb-10">Your request has been sent!</p>
          <p class="font-light text-xs italic mb-10">
            Please check your email.
          </p>
          <a
            href="https://lsu.edu.ph/registrar"
            class="bg-green-800 text-white rounded-3xl py-1.5 px-10 lg:mb-0 mb-5 mx-auto w-fit lg:block hidden text-sm uppercase"
          >
            <i class="fa fa-arrow-circle-left mr-4"></i> Registrar
          </a>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
<style scoped>
input[type="radio"] {
  margin: 3px auto auto auto;
}

.error {
  color: red;
}

.waves {
  position: relative;
  width: 100%;
  height: 15vh;
  margin-bottom: -7px;
  /*Fix for safari gap*/
  min-height: 100px;
  max-height: 150px;
}

/* Animation */
.parallax > use {
  animation: move-forever 25s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
}

.parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}

.parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}

.parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}

.parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}

@keyframes move-forever {
  0% {
    transform: translate3d(-90px, 0, 0);
  }

  100% {
    transform: translate3d(85px, 0, 0);
  }
}

/*Shrinking for mobile*/
@media (max-width: 768px) {
  .waves {
    height: 40px;
    min-height: 40px;
  }

  .content {
    height: 30vh;
  }

  h1 {
    font-size: 24px;
  }
}
</style>
